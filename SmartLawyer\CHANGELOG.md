# سجل التغييرات - Smart Lawyer

جميع التغييرات المهمة في هذا المشروع سيتم توثيقها في هذا الملف.

التنسيق مبني على [Keep a Changelog](https://keepachangelog.com/ar/1.0.0/)،
وهذا المشروع يتبع [Semantic Versioning](https://semver.org/spec/v2.0.0.html).

## [غير منشور]

### مخطط للإضافة
- وحدة إدارة الملفات والقضايا
- نظام الجلسات والمواعيد
- إدارة الإجراءات والمصاريف
- نظام التقارير والإحصائيات
- الإشعارات عبر WhatsApp والبريد الإلكتروني
- نظام النسخ الاحتياطي التلقائي
- إدارة المستخدمين والصلاحيات

## [1.0.0] - 2024-06-27

### أضيف
- **نظام تسجيل الدخول الآمن**
  - واجهة تسجيل دخول أنيقة مقسمة لنصفين
  - التحقق من بيانات المستخدم من قاعدة البيانات
  - عرض معلومات البرنامج والمطور
  - دعم تبديل اللغة في شاشة تسجيل الدخول

- **النافذة الرئيسية**
  - تصميم احترافي مع قائمة جانبية تفاعلية
  - لوحة تحكم مركزية
  - شريط علوي يحتوي على الترحيب والتاريخ
  - أزرار ملونة للوحدات المختلفة
  - تأثيرات بصرية عند التمرير فوق الأزرار

- **نظام تعدد اللغات**
  - دعم كامل للغة العربية والفرنسية
  - إمكانية التبديل بين اللغات في الوقت الفعلي
  - دعم النصوص من اليمين إلى اليسار للعربية
  - ترجمة شاملة لجميع عناصر الواجهة

- **وحدة إدارة الموكلين**
  - إضافة موكلين جدد مع تصنيفهم (فرد، شركة، إدارة)
  - تعديل بيانات الموكلين الموجودين
  - حذف الموكلين مع التحقق من عدم ارتباطهم بملفات
  - عرض قائمة الموكلين في جدول منسق
  - بحث متقدم في بيانات الموكلين
  - فلترة حسب نوع الموكل
  - حفظ معلومات الاتصال والملاحظات

- **قاعدة البيانات SQLite**
  - تصميم شامل لجميع الجداول المطلوبة
  - إنشاء تلقائي لقاعدة البيانات عند التشغيل الأول
  - إدراج البيانات الافتراضية (مستخدم admin)
  - إدارة الاتصالات باستخدام نمط Singleton
  - دعم المعاملات والاستعلامات المعقدة

- **نماذج البيانات (Models)**
  - فئات شاملة لجميع الكيانات
  - خصائص محسوبة للعرض
  - علاقات بين الجداول
  - تحقق من صحة البيانات

- **ملفات الإعداد والتوثيق**
  - ملف README شامل باللغة العربية
  - دليل التثبيت المفصل
  - ملفات batch للتشغيل والبناء
  - إعدادات التطبيق في app.config
  - سجل التغييرات

### التقنيات المستخدمة
- **الإطار**: .NET 6.0 Windows Forms
- **قاعدة البيانات**: SQLite 3
- **الحزم**:
  - System.Data.SQLite v1.0.119
  - iTextSharp v5.5.13.4 (لملفات PDF)
  - MailKit v4.13.0 (للبريد الإلكتروني)
  - Newtonsoft.Json v13.0.3 (لمعالجة JSON)

### الأمان
- تشفير كلمات المرور
- التحقق من صحة المدخلات
- حماية من SQL Injection
- إدارة جلسات المستخدمين

### الأداء
- استخدام Connection Pooling
- تحسين الاستعلامات
- تحميل البيانات عند الحاجة
- إدارة الذاكرة بكفاءة

### واجهة المستخدم
- تصميم حديث ومتجاوب
- ألوان متناسقة ومريحة للعين
- خطوط واضحة ومقروءة
- تأثيرات بصرية سلسة
- دعم الشاشات عالية الدقة

## [0.1.0] - 2024-06-26

### أضيف
- إعداد المشروع الأولي
- هيكل المجلدات الأساسي
- إعدادات .NET 6.0 Windows Forms

---

## أنواع التغييرات

- **أضيف** للميزات الجديدة
- **غُيّر** للتغييرات في الميزات الموجودة
- **مُهمل** للميزات التي ستُحذف قريباً
- **حُذف** للميزات المحذوفة
- **أُصلح** لإصلاح الأخطاء
- **أمان** في حالة الثغرات الأمنية

## معلومات الإصدارات

### نظام الترقيم
- **الإصدار الرئيسي** (Major): تغييرات جذرية غير متوافقة
- **الإصدار الفرعي** (Minor): إضافة ميزات جديدة متوافقة
- **الإصلاح** (Patch): إصلاح أخطاء متوافق

### دورة الإصدارات
- **إصدارات رئيسية**: كل 6-12 شهر
- **إصدارات فرعية**: كل 1-3 أشهر  
- **إصلاحات**: حسب الحاجة

### الدعم
- **الإصدار الحالي**: دعم كامل
- **الإصدار السابق**: دعم الأمان فقط
- **الإصدارات الأقدم**: بدون دعم

---

**ملاحظة**: هذا السجل يتم تحديثه مع كل إصدار جديد. للحصول على آخر التحديثات، يرجى زيارة الموقع الرسمي أو التواصل مع فريق التطوير.
