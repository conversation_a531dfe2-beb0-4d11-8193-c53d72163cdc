using System;
using System.Data;
using System.Data.SQLite;
using System.Drawing;
using System.Linq;
using System.Windows.Forms;
using SmartLawyer.Database;
using SmartLawyer.Localization;
using SmartLawyer.Models;
using SmartLawyer.UI;

namespace SmartLawyer.Forms
{
    public partial class ClientsForm : Form
    {
        private DataGridView dgvClients;
        private ModernPanel topPanel;
        private ModernButton btnAdd;
        private ModernButton btnEdit;
        private ModernButton btnDelete;
        private ModernButton btnRefresh;
        private ModernTextBox txtSearch;
        private ComboBox cmbClientType;
        private Label lblSearch;
        private Label lblClientType;

        public ClientsForm()
        {
            InitializeComponent();
            LoadClients();
            LoadLanguage();
            ApplyTheme();

            // تطبيق إعدادات اللغة
            LanguageManager.Instance.ApplyLanguageToForm(this);

            // تطبيق انتقالات الظهور
            this.Load += ClientsForm_Load;
        }

        private void ClientsForm_Load(object sender, EventArgs e)
        {
            // تأثيرات الظهور
            AnimationManager.SlideIn(topPanel, SlideDirection.FromTop, 400);
            AnimationManager.SlideIn(dgvClients, SlideDirection.FromBottom, 500);
        }

        private void InitializeComponent()
        {
            this.SuspendLayout();

            // إعداد النافذة
            this.Text = "إدارة الموكلين";
            this.Size = new Size(1200, 700);
            this.StartPosition = FormStartPosition.CenterParent;
            this.BackColor = Color.FromArgb(240, 240, 240);

            // اللوحة العلوية
            topPanel = new ModernPanel
            {
                Size = new Size(1200, 80),
                Location = new Point(0, 0),
                BackColor = ThemeManager.Instance.GetSurfaceColor(),
                Dock = DockStyle.Top,
                BorderRadius = 0,
                HasShadow = true
            };

            // أزرار الإجراءات
            btnAdd = new ModernButton
            {
                Text = "إضافة موكل جديد",
                Location = new Point(20, 20),
                Size = new Size(150, 40),
                Font = new Font("Segoe UI", 10, FontStyle.Bold),
                BackColor = Color.FromArgb(46, 204, 113),
                ForeColor = Color.White,
                BorderRadius = 8
            };
            btnAdd.Click += BtnAdd_Click;

            btnEdit = new ModernButton
            {
                Text = "تعديل",
                Location = new Point(180, 20),
                Size = new Size(100, 40),
                Font = new Font("Segoe UI", 10, FontStyle.Bold),
                BackColor = Color.FromArgb(52, 152, 219),
                ForeColor = Color.White,
                BorderRadius = 8,
                Enabled = false
            };
            btnEdit.Click += BtnEdit_Click;

            btnDelete = new ModernButton
            {
                Text = "حذف",
                Location = new Point(290, 20),
                Size = new Size(100, 40),
                Font = new Font("Segoe UI", 10, FontStyle.Bold),
                BackColor = Color.FromArgb(231, 76, 60),
                ForeColor = Color.White,
                BorderRadius = 8,
                Enabled = false
            };
            btnDelete.Click += BtnDelete_Click;

            btnRefresh = new ModernButton
            {
                Text = "تحديث",
                Location = new Point(400, 20),
                Size = new Size(100, 40),
                Font = new Font("Segoe UI", 10, FontStyle.Bold),
                BackColor = Color.FromArgb(155, 89, 182),
                ForeColor = Color.White,
                BorderRadius = 8
            };
            btnRefresh.Click += BtnRefresh_Click;

            // البحث
            lblSearch = new Label
            {
                Text = "بحث:",
                Location = new Point(950, 25),
                Size = new Size(50, 30),
                Font = new Font("Segoe UI", 10, FontStyle.Bold),
                TextAlign = ContentAlignment.MiddleRight
            };

            txtSearch = new ModernTextBox
            {
                Location = new Point(750, 25),
                Size = new Size(190, 35),
                Font = new Font("Segoe UI", 10),
                PlaceholderText = "بحث..."
            };
            txtSearch.TextChanged += TxtSearch_TextChanged;

            // تصنيف الموكلين
            lblClientType = new Label
            {
                Text = "نوع الموكل:",
                Location = new Point(680, 25),
                Size = new Size(80, 30),
                Font = new Font("Segoe UI", 10, FontStyle.Bold),
                TextAlign = ContentAlignment.MiddleRight
            };

            cmbClientType = new ComboBox
            {
                Location = new Point(520, 25),
                Size = new Size(150, 30),
                Font = new Font("Segoe UI", 10),
                DropDownStyle = ComboBoxStyle.DropDownList
            };
            cmbClientType.Items.Add("الكل");
            cmbClientType.Items.Add("فرد");
            cmbClientType.Items.Add("شركة");
            cmbClientType.Items.Add("إدارة");
            cmbClientType.SelectedIndex = 0;
            cmbClientType.SelectedIndexChanged += CmbClientType_SelectedIndexChanged;

            // إضافة العناصر إلى اللوحة العلوية
            topPanel.Controls.Add(btnAdd);
            topPanel.Controls.Add(btnEdit);
            topPanel.Controls.Add(btnDelete);
            topPanel.Controls.Add(btnRefresh);
            topPanel.Controls.Add(lblSearch);
            topPanel.Controls.Add(txtSearch);
            topPanel.Controls.Add(lblClientType);
            topPanel.Controls.Add(cmbClientType);

            // جدول البيانات
            dgvClients = new DataGridView
            {
                Location = new Point(20, 100),
                Size = new Size(1160, 560),
                BackgroundColor = Color.White,
                BorderStyle = BorderStyle.None,
                AllowUserToAddRows = false,
                AllowUserToDeleteRows = false,
                ReadOnly = true,
                SelectionMode = DataGridViewSelectionMode.FullRowSelect,
                MultiSelect = false,
                AutoSizeColumnsMode = DataGridViewAutoSizeColumnsMode.Fill,
                ColumnHeadersHeightSizeMode = DataGridViewColumnHeadersHeightSizeMode.AutoSize,
                RowHeadersVisible = false,
                Font = new Font("Segoe UI", 10),
                RightToLeft = RightToLeft.Yes
            };

            // تنسيق رأس الجدول
            dgvClients.ColumnHeadersDefaultCellStyle.BackColor = Color.FromArgb(52, 73, 94);
            dgvClients.ColumnHeadersDefaultCellStyle.ForeColor = Color.White;
            dgvClients.ColumnHeadersDefaultCellStyle.Font = new Font("Segoe UI", 11, FontStyle.Bold);
            dgvClients.ColumnHeadersDefaultCellStyle.Alignment = DataGridViewContentAlignment.MiddleCenter;

            // تنسيق الصفوف
            dgvClients.DefaultCellStyle.SelectionBackColor = Color.FromArgb(52, 152, 219);
            dgvClients.DefaultCellStyle.SelectionForeColor = Color.White;
            dgvClients.AlternatingRowsDefaultCellStyle.BackColor = Color.FromArgb(248, 249, 250);

            dgvClients.SelectionChanged += DgvClients_SelectionChanged;
            dgvClients.CellDoubleClick += DgvClients_CellDoubleClick;

            // إضافة العناصر إلى النافذة
            this.Controls.Add(topPanel);
            this.Controls.Add(dgvClients);

            this.ResumeLayout(false);
        }

        private void LoadLanguage()
        {
            var lang = LanguageManager.Instance;
            
            this.Text = lang.GetText("Clients");
            btnAdd.Text = lang.GetText("AddClient");
            btnEdit.Text = lang.GetText("Edit");
            btnDelete.Text = lang.GetText("Delete");
            btnRefresh.Text = lang.GetText("Refresh");
            lblSearch.Text = lang.GetText("Search") + ":";
            lblClientType.Text = lang.GetText("ClientType") + ":";
            
            // تحديث عناصر القائمة المنسدلة
            cmbClientType.Items.Clear();
            cmbClientType.Items.Add("الكل");
            cmbClientType.Items.Add(lang.GetText("Individual"));
            cmbClientType.Items.Add(lang.GetText("Company"));
            cmbClientType.Items.Add(lang.GetText("Administration"));
            cmbClientType.SelectedIndex = 0;
        }

        private void LoadClients()
        {
            try
            {
                using (var connection = DatabaseManager.Instance.GetConnection())
                {
                    connection.Open();
                    string query = @"
                        SELECT Id, ClientType, FirstName, LastName, CompanyName, 
                               CIN, ICE, Phone, Email, City, CreatedDate
                        FROM Clients 
                        ORDER BY CreatedDate DESC";

                    using (var adapter = new SQLiteDataAdapter(query, connection))
                    {
                        DataTable dt = new DataTable();
                        adapter.Fill(dt);

                        // إضافة عمود الاسم الكامل
                        dt.Columns.Add("FullName", typeof(string));
                        foreach (DataRow row in dt.Rows)
                        {
                            string clientType = row["ClientType"].ToString();
                            if (clientType == "Company" || clientType == "Administration")
                            {
                                row["FullName"] = row["CompanyName"].ToString();
                            }
                            else
                            {
                                row["FullName"] = $"{row["FirstName"]} {row["LastName"]}";
                            }
                        }

                        dgvClients.DataSource = dt;
                        SetupDataGridViewColumns();
                    }
                }
            }
            catch (Exception ex)
            {
                MessageBox.Show($"خطأ في تحميل بيانات الموكلين: {ex.Message}", "خطأ", 
                               MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        private void SetupDataGridViewColumns()
        {
            if (dgvClients.Columns.Count > 0)
            {
                dgvClients.Columns["Id"].Visible = false;
                dgvClients.Columns["FirstName"].Visible = false;
                dgvClients.Columns["LastName"].Visible = false;
                dgvClients.Columns["CompanyName"].Visible = false;

                dgvClients.Columns["FullName"].HeaderText = "الاسم الكامل";
                dgvClients.Columns["ClientType"].HeaderText = "نوع الموكل";
                dgvClients.Columns["CIN"].HeaderText = "رقم البطاقة الوطنية";
                dgvClients.Columns["ICE"].HeaderText = "رقم السجل التجاري";
                dgvClients.Columns["Phone"].HeaderText = "الهاتف";
                dgvClients.Columns["Email"].HeaderText = "البريد الإلكتروني";
                dgvClients.Columns["City"].HeaderText = "المدينة";
                dgvClients.Columns["CreatedDate"].HeaderText = "تاريخ الإنشاء";

                // ترتيب الأعمدة
                dgvClients.Columns["FullName"].DisplayIndex = 0;
                dgvClients.Columns["ClientType"].DisplayIndex = 1;
                dgvClients.Columns["Phone"].DisplayIndex = 2;
                dgvClients.Columns["Email"].DisplayIndex = 3;
                dgvClients.Columns["City"].DisplayIndex = 4;
                dgvClients.Columns["CreatedDate"].DisplayIndex = 5;
            }
        }

        private void DgvClients_SelectionChanged(object sender, EventArgs e)
        {
            bool hasSelection = dgvClients.SelectedRows.Count > 0;
            btnEdit.Enabled = hasSelection;
            btnDelete.Enabled = hasSelection;
        }

        private void DgvClients_CellDoubleClick(object sender, DataGridViewCellEventArgs e)
        {
            if (e.RowIndex >= 0)
            {
                BtnEdit_Click(sender, e);
            }
        }

        private void BtnAdd_Click(object sender, EventArgs e)
        {
            ClientEditForm editForm = new ClientEditForm();
            if (editForm.ShowDialog() == DialogResult.OK)
            {
                LoadClients();
            }
        }

        private void BtnEdit_Click(object sender, EventArgs e)
        {
            if (dgvClients.SelectedRows.Count > 0)
            {
                int clientId = Convert.ToInt32(dgvClients.SelectedRows[0].Cells["Id"].Value);
                ClientEditForm editForm = new ClientEditForm(clientId);
                if (editForm.ShowDialog() == DialogResult.OK)
                {
                    LoadClients();
                }
            }
        }

        private void BtnDelete_Click(object sender, EventArgs e)
        {
            if (dgvClients.SelectedRows.Count > 0)
            {
                var result = MessageBox.Show(
                    LanguageManager.Instance.GetText("ConfirmDelete"),
                    LanguageManager.Instance.GetText("DeleteConfirmation"),
                    MessageBoxButtons.YesNo,
                    MessageBoxIcon.Question);

                if (result == DialogResult.Yes)
                {
                    int clientId = Convert.ToInt32(dgvClients.SelectedRows[0].Cells["Id"].Value);
                    DeleteClient(clientId);
                }
            }
        }

        private void DeleteClient(int clientId)
        {
            try
            {
                using (var connection = DatabaseManager.Instance.GetConnection())
                {
                    connection.Open();
                    
                    // التحقق من وجود ملفات مرتبطة بالموكل
                    string checkQuery = "SELECT COUNT(*) FROM Cases WHERE ClientId = @clientId";
                    using (var checkCommand = new SQLiteCommand(checkQuery, connection))
                    {
                        checkCommand.Parameters.AddWithValue("@clientId", clientId);
                        int caseCount = Convert.ToInt32(checkCommand.ExecuteScalar());
                        
                        if (caseCount > 0)
                        {
                            MessageBox.Show("لا يمكن حذف هذا الموكل لأنه مرتبط بملفات قضائية", "تحذير", 
                                           MessageBoxButtons.OK, MessageBoxIcon.Warning);
                            return;
                        }
                    }

                    string deleteQuery = "DELETE FROM Clients WHERE Id = @clientId";
                    using (var command = new SQLiteCommand(deleteQuery, connection))
                    {
                        command.Parameters.AddWithValue("@clientId", clientId);
                        command.ExecuteNonQuery();
                    }

                    MessageBox.Show(LanguageManager.Instance.GetText("SaveSuccess"), "نجح", 
                                   MessageBoxButtons.OK, MessageBoxIcon.Information);
                    LoadClients();
                }
            }
            catch (Exception ex)
            {
                MessageBox.Show($"خطأ في حذف الموكل: {ex.Message}", "خطأ", 
                               MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        private void BtnRefresh_Click(object sender, EventArgs e)
        {
            LoadClients();
        }

        private void TxtSearch_TextChanged(object sender, EventArgs e)
        {
            FilterClients();
        }

        private void CmbClientType_SelectedIndexChanged(object sender, EventArgs e)
        {
            FilterClients();
        }

        private void FilterClients()
        {
            if (dgvClients.DataSource is DataTable dt)
            {
                string filter = "";

                // فلترة حسب النص
                if (!string.IsNullOrWhiteSpace(txtSearch.Text))
                {
                    filter += $"(FullName LIKE '%{txtSearch.Text}%' OR Phone LIKE '%{txtSearch.Text}%' OR Email LIKE '%{txtSearch.Text}%')";
                }

                // فلترة حسب نوع الموكل
                if (cmbClientType.SelectedIndex > 0)
                {
                    string clientType = "";
                    switch (cmbClientType.SelectedIndex)
                    {
                        case 1: clientType = "Individual"; break;
                        case 2: clientType = "Company"; break;
                        case 3: clientType = "Administration"; break;
                    }

                    if (!string.IsNullOrEmpty(filter))
                        filter += " AND ";
                    filter += $"ClientType = '{clientType}'";
                }

                dt.DefaultView.RowFilter = filter;
            }
        }

        private void ApplyTheme()
        {
            var theme = ThemeManager.Instance;

            // تطبيق ألوان الثيم
            this.BackColor = theme.GetBackgroundColor();
            topPanel.BackColor = theme.GetSurfaceColor();
            dgvClients.BackgroundColor = theme.GetSurfaceColor();

            // تحديث ألوان النصوص
            lblSearch.ForeColor = theme.GetTextColor();
            lblClientType.ForeColor = theme.GetTextColor();

            // تطبيق الخطوط
            var currentLang = LanguageManager.Instance.CurrentLanguageInfo;
            Font newFont = new Font(currentLang.FontFamily, 10);
            Font boldFont = new Font(currentLang.FontFamily, 10, FontStyle.Bold);

            lblSearch.Font = boldFont;
            lblClientType.Font = boldFont;
            txtSearch.Font = newFont;
            cmbClientType.Font = newFont;

            // تحديث ألوان الجدول
            dgvClients.ColumnHeadersDefaultCellStyle.BackColor = theme.GetPrimaryColor();
            dgvClients.ColumnHeadersDefaultCellStyle.ForeColor = Color.White;
            dgvClients.DefaultCellStyle.SelectionBackColor = theme.GetPrimaryColor();
            dgvClients.AlternatingRowsDefaultCellStyle.BackColor = theme.GetBackgroundColor();
        }
    }
}
