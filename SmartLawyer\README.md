# سمارت لوير - Smart Lawyer
## برنامج إدارة مكتب المحاماة في المغرب

### 📋 نظرة عامة
سمارت لوير هو برنامج احترافي شامل لإدارة مكاتب المحاماة في المغرب، مطور باستخدام C# و WinForms. يوفر البرنامج حلولاً متكاملة لإدارة جميع جوانب عمل المحامي من الموكلين والملفات إلى الجلسات والمصاريف.

### ✨ المميزات الرئيسية

#### 🔐 نظام تسجيل الدخول الآمن
- واجهة تسجيل دخول أنيقة ومقسمة لنصفين
- نظام مستخدمين متعدد المستويات
- حماية بكلمة مرور مشفرة

#### 🌐 دعم اللغات المتعددة
- واجهة ثنائية اللغة (العربية والفرنسية)
- إمكانية التبديل بين اللغات في الوقت الفعلي
- دعم كامل للنصوص من اليمين إلى اليسار

#### 👥 إدارة الموكلين
- تصنيف الموكلين (أفراد، شركات، إدارات)
- إضافة وتعديل وحذف بيانات الموكلين
- بحث متقدم وفلترة حسب النوع
- حفظ معلومات الاتصال والملاحظات

#### 📁 إدارة الملفات والقضايا
- فتح ملفات جديدة وربطها بالموكلين
- تصنيف القضايا (مدني، تجاري، جنائي، إداري)
- تتبع حالة القضايا ومراحل تطورها
- إرفاق المستندات الرقمية

#### ⚖️ مذكرة الجلسات
- تسجيل الجلسات القادمة والسابقة
- ربط الجلسات بالقضايا المعنية
- تحديد المحكمة والقاعة والقاضي
- نظام تنبيهات تلقائي

#### 📅 إدارة المواعيد
- تسجيل المواعيد مع الموكلين
- تحديد التاريخ والوقت والمكان
- إشعارات تلقائية قبل الموعد

#### 📋 تسجيل الإجراءات
- توثيق جميع الإجراءات القانونية
- تصنيف الإجراءات (استدعاء، تبليغ، خبرة، طعن)
- تتبع تنفيذ الإجراءات وحالتها

#### 💰 إدارة المصاريف
- تسجيل مصاريف المكتب والملفات
- تصنيف المصاريف حسب النوع
- تقارير مفصلة عن المصاريف

#### 👤 نظام المستخدمين
- إدارة المستخدمين بصلاحيات مختلفة
- أدوار متعددة (محامي، سكرتير، مدير)
- سجل نشاطات المستخدمين

#### 🔔 نظام الإشعارات
- إشعارات تلقائية للجلسات والمواعيد
- دعم البريد الإلكتروني و WhatsApp
- إعدادات قابلة للتخصيص

#### 📊 التقارير والإحصائيات
- تقارير شاملة للقضايا والموكلين
- إحصائيات مفصلة حسب الفترة
- تصدير التقارير بصيغة PDF و Excel

#### ⚙️ لوحة التحكم
- إعدادات شاملة للنظام
- نسخ احتياطي تلقائي ويدوي
- إدارة قاعدة البيانات

### 🛠️ المتطلبات التقنية

#### البرمجة
- **اللغة**: C# (.NET 6.0)
- **الواجهة**: Windows Forms
- **قاعدة البيانات**: SQLite (محلية)

#### النظام
- **نظام التشغيل**: Windows 7 أو أحدث
- **الذاكرة**: 4 GB RAM (الحد الأدنى)
- **المساحة**: 500 MB مساحة فارغة
- **الشاشة**: دقة 1024x768 أو أعلى

#### الحزم المستخدمة
- `System.Data.SQLite` - قاعدة البيانات
- `iTextSharp` - إنشاء ملفات PDF
- `MailKit` - إرسال الإيميلات
- `Newtonsoft.Json` - معالجة JSON

### 🚀 التثبيت والتشغيل

#### متطلبات التطوير
```bash
# تثبيت .NET 6.0 SDK
# تحميل من: https://dotnet.microsoft.com/download/dotnet/6.0

# استنساخ المشروع
git clone [repository-url]
cd SmartLawyer

# استعادة الحزم
dotnet restore

# بناء المشروع
dotnet build

# تشغيل البرنامج
dotnet run
```

#### بيانات تسجيل الدخول الافتراضية
- **اسم المستخدم**: admin
- **كلمة المرور**: admin123

### 📁 هيكل المشروع

```
SmartLawyer/
├── Database/
│   └── DatabaseManager.cs      # إدارة قاعدة البيانات
├── Forms/
│   ├── LoginForm.cs            # نافذة تسجيل الدخول
│   ├── ClientsForm.cs          # نافذة إدارة الموكلين
│   └── ClientEditForm.cs       # نافذة تحرير الموكل
├── Localization/
│   └── LanguageManager.cs      # إدارة اللغات
├── Models/
│   └── Client.cs               # نماذج البيانات
├── Form1.cs                    # النافذة الرئيسية
├── Program.cs                  # نقطة البداية
└── SmartLawyer.csproj         # ملف المشروع
```

### 🎯 الوحدات المكتملة

✅ **نظام تسجيل الدخول**
- واجهة أنيقة مع شعار البرنامج
- التحقق من بيانات المستخدم
- دعم تعدد اللغات

✅ **النافذة الرئيسية**
- قائمة جانبية تفاعلية
- لوحة تحكم مركزية
- تبديل اللغات

✅ **إدارة الموكلين**
- إضافة وتعديل وحذف الموكلين
- تصنيف حسب النوع
- بحث وفلترة متقدمة

✅ **قاعدة البيانات**
- تصميم شامل لجميع الجداول
- إنشاء تلقائي للبيانات الافتراضية
- إدارة الاتصالات

✅ **نظام اللغات**
- دعم العربية والفرنسية
- ترجمة شاملة للواجهات
- تبديل فوري للغة

### 🔄 الوحدات قيد التطوير

🚧 **إدارة الملفات والقضايا**
🚧 **مذكرة الجلسات**
🚧 **إدارة المواعيد**
🚧 **تسجيل الإجراءات**
🚧 **إدارة المصاريف**
🚧 **نظام المستخدمين**
🚧 **الإشعارات**
🚧 **التقارير والإحصائيات**
🚧 **الإعدادات المتقدمة**

### 👨‍💻 معلومات المطور

**Generation Five**
- **الهاتف**: +212 661 77 53 72
- **الموقع**: www.generationfive.net
- **البريد الإلكتروني**: <EMAIL>

### 📄 الترخيص

هذا البرنامج مطور خصيصاً لإدارة مكاتب المحاماة في المغرب.
جميع الحقوق محفوظة © 2024 Generation Five

### 🤝 المساهمة

نرحب بالمساهمات لتطوير البرنامج:
1. Fork المشروع
2. إنشاء فرع للميزة الجديدة
3. Commit التغييرات
4. Push إلى الفرع
5. إنشاء Pull Request

### 📞 الدعم الفني

للحصول على الدعم الفني أو الاستفسارات:
- **الهاتف**: +212 661 77 53 72
- **البريد الإلكتروني**: <EMAIL>
- **ساعات العمل**: من الاثنين إلى الجمعة، 9:00 - 18:00

---

**ملاحظة**: هذا البرنامج في مرحلة التطوير النشط. الوحدات الجديدة تتم إضافتها بانتظام.
