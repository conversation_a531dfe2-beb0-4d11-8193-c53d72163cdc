using System;
using System.Drawing;
using System.Drawing.Drawing2D;
using System.Windows.Forms;
using WinFormsTimer = System.Windows.Forms.Timer;

namespace SmartLawyer.UI
{
    public static class AnimationManager
    {
        public static void FadeIn(Control control, int duration = 500)
        {
            control.Visible = true;
            WinFormsTimer timer = new WinFormsTimer();
            timer.Interval = 16; // ~60 FPS
            
            DateTime startTime = DateTime.Now;
            double startOpacity = 0;
            double targetOpacity = 1;
            
            timer.Tick += (sender, e) =>
            {
                double elapsed = (DateTime.Now - startTime).TotalMilliseconds;
                double progress = Math.Min(elapsed / duration, 1.0);
                
                // منحنى ease-out
                progress = 1 - Math.Pow(1 - progress, 3);
                
                double currentOpacity = startOpacity + (targetOpacity - startOpacity) * progress;
                
                // تطبيق الشفافية (محاكاة)
                control.BackColor = Color.FromArgb(
                    (int)(255 * currentOpacity),
                    control.BackColor.R,
                    control.BackColor.G,
                    control.BackColor.B
                );
                
                if (progress >= 1.0)
                {
                    timer.Stop();
                    timer.Dispose();
                }
            };
            
            timer.Start();
        }

        public static void SlideIn(Control control, SlideDirection direction, int duration = 400)
        {
            Point originalLocation = control.Location;
            Point startLocation = originalLocation;
            
            switch (direction)
            {
                case SlideDirection.FromLeft:
                    startLocation.X -= control.Width;
                    break;
                case SlideDirection.FromRight:
                    startLocation.X += control.Width;
                    break;
                case SlideDirection.FromTop:
                    startLocation.Y -= control.Height;
                    break;
                case SlideDirection.FromBottom:
                    startLocation.Y += control.Height;
                    break;
            }
            
            control.Location = startLocation;
            control.Visible = true;
            
            WinFormsTimer timer = new WinFormsTimer();
            timer.Interval = 16;
            DateTime startTime = DateTime.Now;

            timer.Tick += (sender, e) =>
            {
                double elapsed = (DateTime.Now - startTime).TotalMilliseconds;
                double progress = Math.Min(elapsed / duration, 1.0);

                // منحنى ease-out
                progress = 1 - Math.Pow(1 - progress, 3);

                int currentX = (int)(startLocation.X + (originalLocation.X - startLocation.X) * progress);
                int currentY = (int)(startLocation.Y + (originalLocation.Y - startLocation.Y) * progress);

                control.Location = new Point(currentX, currentY);

                if (progress >= 1.0)
                {
                    control.Location = originalLocation;
                    timer.Stop();
                    timer.Dispose();
                }
            };
            
            timer.Start();
        }

        public static void ScaleIn(Control control, int duration = 300)
        {
            Size originalSize = control.Size;
            Point originalLocation = control.Location;
            
            control.Size = new Size(0, 0);
            control.Location = new Point(
                originalLocation.X + originalSize.Width / 2,
                originalLocation.Y + originalSize.Height / 2
            );
            control.Visible = true;
            
            WinFormsTimer timer = new WinFormsTimer();
            timer.Interval = 16;
            DateTime startTime = DateTime.Now;

            timer.Tick += (sender, e) =>
            {
                double elapsed = (DateTime.Now - startTime).TotalMilliseconds;
                double progress = Math.Min(elapsed / duration, 1.0);

                // منحنى ease-out مع تأثير مرتد
                progress = 1 - Math.Pow(1 - progress, 3);
                if (progress > 0.8)
                {
                    progress = 0.8 + (progress - 0.8) * 1.2; // تأثير مرتد خفيف
                }

                int currentWidth = (int)(originalSize.Width * progress);
                int currentHeight = (int)(originalSize.Height * progress);

                control.Size = new Size(currentWidth, currentHeight);
                control.Location = new Point(
                    originalLocation.X + (originalSize.Width - currentWidth) / 2,
                    originalLocation.Y + (originalSize.Height - currentHeight) / 2
                );

                if (progress >= 1.0)
                {
                    control.Size = originalSize;
                    control.Location = originalLocation;
                    timer.Stop();
                    timer.Dispose();
                }
            };
            
            timer.Start();
        }

        public static void Pulse(Control control, int pulseCount = 3, int duration = 200)
        {
            Size originalSize = control.Size;
            int currentPulse = 0;
            
            WinFormsTimer timer = new WinFormsTimer();
            timer.Interval = 16;
            DateTime startTime = DateTime.Now;
            bool expanding = true;
            
            timer.Tick += (sender, e) =>
            {
                double elapsed = (DateTime.Now - startTime).TotalMilliseconds;
                double cycleProgress = (elapsed % duration) / duration;
                
                if (cycleProgress < 0.5)
                {
                    // التوسع
                    double progress = cycleProgress * 2;
                    int scaleAmount = (int)(10 * progress);
                    control.Size = new Size(
                        originalSize.Width + scaleAmount,
                        originalSize.Height + scaleAmount
                    );
                }
                else
                {
                    // الانكماش
                    double progress = (cycleProgress - 0.5) * 2;
                    int scaleAmount = (int)(10 * (1 - progress));
                    control.Size = new Size(
                        originalSize.Width + scaleAmount,
                        originalSize.Height + scaleAmount
                    );
                }
                
                if (elapsed >= duration)
                {
                    currentPulse++;
                    startTime = DateTime.Now;
                    
                    if (currentPulse >= pulseCount)
                    {
                        control.Size = originalSize;
                        timer.Stop();
                        timer.Dispose();
                    }
                }
            };
            
            timer.Start();
        }

        public static void Shake(Control control, int intensity = 5, int duration = 500)
        {
            Point originalLocation = control.Location;
            Random random = new Random();
            
            WinFormsTimer timer = new WinFormsTimer();
            timer.Interval = 50;
            DateTime startTime = DateTime.Now;
            
            timer.Tick += (sender, e) =>
            {
                double elapsed = (DateTime.Now - startTime).TotalMilliseconds;
                double progress = elapsed / duration;
                
                if (progress >= 1.0)
                {
                    control.Location = originalLocation;
                    timer.Stop();
                    timer.Dispose();
                    return;
                }
                
                // تقليل الشدة تدريجياً
                int currentIntensity = (int)(intensity * (1 - progress));
                
                int offsetX = random.Next(-currentIntensity, currentIntensity + 1);
                int offsetY = random.Next(-currentIntensity, currentIntensity + 1);
                
                control.Location = new Point(
                    originalLocation.X + offsetX,
                    originalLocation.Y + offsetY
                );
            };
            
            timer.Start();
        }

        public static void HighlightControl(Control control, Color highlightColor, int duration = 1000)
        {
            Color originalBackColor = control.BackColor;
            
            WinFormsTimer timer = new WinFormsTimer();
            timer.Interval = 16;
            DateTime startTime = DateTime.Now;
            
            timer.Tick += (sender, e) =>
            {
                double elapsed = (DateTime.Now - startTime).TotalMilliseconds;
                double progress = elapsed / duration;
                
                if (progress >= 1.0)
                {
                    control.BackColor = originalBackColor;
                    timer.Stop();
                    timer.Dispose();
                    return;
                }
                
                // تأثير النبض للإضاءة
                double intensity = Math.Sin(progress * Math.PI * 4) * 0.5 + 0.5;
                intensity *= (1 - progress); // تقليل الشدة تدريجياً
                
                int r = (int)(originalBackColor.R + (highlightColor.R - originalBackColor.R) * intensity);
                int g = (int)(originalBackColor.G + (highlightColor.G - originalBackColor.G) * intensity);
                int b = (int)(originalBackColor.B + (highlightColor.B - originalBackColor.B) * intensity);
                
                control.BackColor = Color.FromArgb(r, g, b);
            };
            
            timer.Start();
        }

        public static void AnimateFormLoad(Form form)
        {
            form.Opacity = 0;
            form.Show();
            
            WinFormsTimer timer = new WinFormsTimer();
            timer.Interval = 16;
            DateTime startTime = DateTime.Now;
            int duration = 400;
            
            timer.Tick += (sender, e) =>
            {
                double elapsed = (DateTime.Now - startTime).TotalMilliseconds;
                double progress = Math.Min(elapsed / duration, 1.0);
                
                // منحنى ease-out
                progress = 1 - Math.Pow(1 - progress, 3);
                
                form.Opacity = progress;
                
                if (progress >= 1.0)
                {
                    timer.Stop();
                    timer.Dispose();
                }
            };
            
            timer.Start();
        }
    }

    public enum SlideDirection
    {
        FromLeft,
        FromRight,
        FromTop,
        FromBottom,
        ToLeft,
        ToRight,
        ToTop,
        ToBottom
    }

    public static class AdvancedAnimations
    {
        // تأثير Parallax للخلفيات
        public static void CreateParallaxEffect(Control backgroundControl, Control foregroundControl, float speed = 0.5f)
        {
            if (backgroundControl?.Parent == null || foregroundControl?.Parent == null) return;

            var parent = backgroundControl.Parent;

            parent.MouseMove += (sender, e) =>
            {
                if (!UISettings.Instance.EnableAnimations) return;

                var centerX = parent.Width / 2;
                var centerY = parent.Height / 2;

                var offsetX = (e.X - centerX) * speed;
                var offsetY = (e.Y - centerY) * speed;

                backgroundControl.Location = new Point(
                    (int)(backgroundControl.Tag is Point originalPos ? originalPos.X + offsetX : offsetX),
                    (int)(backgroundControl.Tag is Point originalPos2 ? originalPos2.Y + offsetY : offsetY)
                );

                if (backgroundControl.Tag == null)
                {
                    backgroundControl.Tag = backgroundControl.Location;
                }
            };
        }

        // تأثير انتقال الصفحات (مبسط)
        public static void PageTransition(Control oldPage, Control newPage, PageTransitionType transitionType, int duration = 500)
        {
            if (!UISettings.Instance.EnableAnimations)
            {
                oldPage.Visible = false;
                newPage.Visible = true;
                return;
            }

            // انتقال بسيط
            oldPage.Visible = false;
            newPage.Visible = true;

            // تأثير fade بسيط
            newPage.Opacity = 0;
            WinFormsTimer fadeTimer = new WinFormsTimer();
            fadeTimer.Interval = 50;
            double opacity = 0;

            fadeTimer.Tick += (s, e) =>
            {
                opacity += 0.1;
                if (opacity >= 1.0)
                {
                    newPage.Opacity = 1.0;
                    fadeTimer.Stop();
                    fadeTimer.Dispose();
                }
                else
                {
                    newPage.Opacity = opacity;
                }
            };
            fadeTimer.Start();
        }

        // تأثير موجة الضغط (Ripple Effect)
        public static void CreateRippleEffect(Control control, Point clickPoint, Color rippleColor, int duration = 600)
        {
            if (!UISettings.Instance.EnableAnimations) return;

            var ripplePanel = new Panel
            {
                Size = new Size(0, 0),
                Location = clickPoint,
                BackColor = Color.Transparent,
                Parent = control
            };

            var maxRadius = Math.Max(
                Math.Max(clickPoint.X, control.Width - clickPoint.X),
                Math.Max(clickPoint.Y, control.Height - clickPoint.Y)
            ) * 2;

            WinFormsTimer timer = new WinFormsTimer();
            timer.Interval = 16;
            DateTime startTime = DateTime.Now;

            timer.Tick += (sender, e) =>
            {
                double elapsed = (DateTime.Now - startTime).TotalMilliseconds;
                double progress = Math.Min(elapsed / duration, 1.0);

                int currentRadius = (int)(maxRadius * progress);
                int alpha = (int)(100 * (1 - progress));

                ripplePanel.Size = new Size(currentRadius, currentRadius);
                ripplePanel.Location = new Point(
                    clickPoint.X - currentRadius / 2,
                    clickPoint.Y - currentRadius / 2
                );

                // رسم الدائرة
                ripplePanel.Paint += (s, pe) =>
                {
                    using (var brush = new SolidBrush(Color.FromArgb(alpha, rippleColor)))
                    {
                        pe.Graphics.SmoothingMode = SmoothingMode.AntiAlias;
                        pe.Graphics.FillEllipse(brush, 0, 0, currentRadius, currentRadius);
                    }
                };

                ripplePanel.Invalidate();

                if (progress >= 1.0)
                {
                    timer.Stop();
                    timer.Dispose();
                    control.Controls.Remove(ripplePanel);
                    ripplePanel.Dispose();
                }
            };

            timer.Start();
        }

        // تأثير التحميل المتحرك
        public static void ShowLoadingAnimation(Control container, string message = "جاري التحميل...")
        {
            var loadingPanel = new Panel
            {
                Size = container.Size,
                Location = new Point(0, 0),
                BackColor = Color.FromArgb(200, ThemeManager.Instance.GetBackgroundColor()),
                Parent = container,
                Name = "LoadingPanel"
            };

            var loadingLabel = new Label
            {
                Text = message,
                Font = new Font("Segoe UI", 12, FontStyle.Bold),
                ForeColor = ThemeManager.Instance.GetTextColor(),
                TextAlign = ContentAlignment.MiddleCenter,
                Dock = DockStyle.Fill,
                Parent = loadingPanel
            };

            // تأثير النبض للنص
            WinFormsTimer pulseTimer = new WinFormsTimer();
            pulseTimer.Interval = 800;
            bool growing = true;

            pulseTimer.Tick += (s, e) =>
            {
                if (growing)
                {
                    loadingLabel.Font = new Font(loadingLabel.Font.FontFamily, 13, FontStyle.Bold);
                    growing = false;
                }
                else
                {
                    loadingLabel.Font = new Font(loadingLabel.Font.FontFamily, 12, FontStyle.Bold);
                    growing = true;
                }
            };

            pulseTimer.Start();
            loadingPanel.Tag = pulseTimer; // حفظ المؤقت للإيقاف لاحقاً

            loadingPanel.BringToFront();
        }

        public static void HideLoadingAnimation(Control container)
        {
            var loadingPanel = container.Controls.Find("LoadingPanel", false).FirstOrDefault();
            if (loadingPanel != null)
            {
                if (loadingPanel.Tag is WinFormsTimer timer)
                {
                    timer.Stop();
                    timer.Dispose();
                }

                AnimationManager.FadeOut(loadingPanel, 300);

                WinFormsTimer removeTimer = new WinFormsTimer();
                removeTimer.Interval = 350;
                removeTimer.Tick += (s, e) =>
                {
                    container.Controls.Remove(loadingPanel);
                    loadingPanel.Dispose();
                    removeTimer.Stop();
                    removeTimer.Dispose();
                };
                removeTimer.Start();
            }
        }

        // تأثير التمرير السلس
        public static void SmoothScroll(ScrollableControl control, int targetY, int duration = 500)
        {
            if (!UISettings.Instance.EnableAnimations)
            {
                control.AutoScrollPosition = new Point(0, targetY);
                return;
            }

            var startY = Math.Abs(control.AutoScrollPosition.Y);
            var distance = targetY - startY;

            WinFormsTimer timer = new WinFormsTimer();
            timer.Interval = 16;
            DateTime startTime = DateTime.Now;

            timer.Tick += (sender, e) =>
            {
                double elapsed = (DateTime.Now - startTime).TotalMilliseconds;
                double progress = Math.Min(elapsed / duration, 1.0);

                // منحنى ease-out
                progress = 1 - Math.Pow(1 - progress, 3);

                int currentY = (int)(startY + distance * progress);
                control.AutoScrollPosition = new Point(0, currentY);

                if (progress >= 1.0)
                {
                    control.AutoScrollPosition = new Point(0, targetY);
                    timer.Stop();
                    timer.Dispose();
                }
            };

            timer.Start();
        }
    }

    public enum PageTransitionType
    {
        SlideLeft,
        SlideRight,
        FadeInOut,
        ZoomIn
    }
}
