using System;
using System.Drawing;
using System.Drawing.Drawing2D;
using System.Windows.Forms;
using WinFormsTimer = System.Windows.Forms.Timer;

namespace SmartLawyer.UI
{
    public static class AnimationManager
    {
        public static void FadeIn(Control control, int duration = 500)
        {
            control.Visible = true;
            WinFormsTimer timer = new WinFormsTimer();
            timer.Interval = 16; // ~60 FPS
            
            DateTime startTime = DateTime.Now;
            double startOpacity = 0;
            double targetOpacity = 1;
            
            timer.Tick += (sender, e) =>
            {
                double elapsed = (DateTime.Now - startTime).TotalMilliseconds;
                double progress = Math.Min(elapsed / duration, 1.0);
                
                // منحنى ease-out
                progress = 1 - Math.Pow(1 - progress, 3);
                
                double currentOpacity = startOpacity + (targetOpacity - startOpacity) * progress;
                
                // تطبيق الشفافية (محاكاة)
                control.BackColor = Color.FromArgb(
                    (int)(255 * currentOpacity),
                    control.BackColor.R,
                    control.BackColor.G,
                    control.BackColor.B
                );
                
                if (progress >= 1.0)
                {
                    timer.Stop();
                    timer.Dispose();
                }
            };
            
            timer.Start();
        }

        public static void SlideIn(Control control, SlideDirection direction, int duration = 400)
        {
            Point originalLocation = control.Location;
            Point startLocation = originalLocation;
            
            switch (direction)
            {
                case SlideDirection.FromLeft:
                    startLocation.X -= control.Width;
                    break;
                case SlideDirection.FromRight:
                    startLocation.X += control.Width;
                    break;
                case SlideDirection.FromTop:
                    startLocation.Y -= control.Height;
                    break;
                case SlideDirection.FromBottom:
                    startLocation.Y += control.Height;
                    break;
            }
            
            control.Location = startLocation;
            control.Visible = true;
            
            WinFormsTimer timer = new WinFormsTimer();
            timer.Interval = 16;
            DateTime startTime = DateTime.Now;

            timer.Tick += (sender, e) =>
            {
                double elapsed = (DateTime.Now - startTime).TotalMilliseconds;
                double progress = Math.Min(elapsed / duration, 1.0);

                // منحنى ease-out
                progress = 1 - Math.Pow(1 - progress, 3);

                int currentX = (int)(startLocation.X + (originalLocation.X - startLocation.X) * progress);
                int currentY = (int)(startLocation.Y + (originalLocation.Y - startLocation.Y) * progress);

                control.Location = new Point(currentX, currentY);

                if (progress >= 1.0)
                {
                    control.Location = originalLocation;
                    timer.Stop();
                    timer.Dispose();
                }
            };
            
            timer.Start();
        }

        public static void ScaleIn(Control control, int duration = 300)
        {
            Size originalSize = control.Size;
            Point originalLocation = control.Location;
            
            control.Size = new Size(0, 0);
            control.Location = new Point(
                originalLocation.X + originalSize.Width / 2,
                originalLocation.Y + originalSize.Height / 2
            );
            control.Visible = true;
            
            WinFormsTimer timer = new WinFormsTimer();
            timer.Interval = 16;
            DateTime startTime = DateTime.Now;

            timer.Tick += (sender, e) =>
            {
                double elapsed = (DateTime.Now - startTime).TotalMilliseconds;
                double progress = Math.Min(elapsed / duration, 1.0);

                // منحنى ease-out مع تأثير مرتد
                progress = 1 - Math.Pow(1 - progress, 3);
                if (progress > 0.8)
                {
                    progress = 0.8 + (progress - 0.8) * 1.2; // تأثير مرتد خفيف
                }

                int currentWidth = (int)(originalSize.Width * progress);
                int currentHeight = (int)(originalSize.Height * progress);

                control.Size = new Size(currentWidth, currentHeight);
                control.Location = new Point(
                    originalLocation.X + (originalSize.Width - currentWidth) / 2,
                    originalLocation.Y + (originalSize.Height - currentHeight) / 2
                );

                if (progress >= 1.0)
                {
                    control.Size = originalSize;
                    control.Location = originalLocation;
                    timer.Stop();
                    timer.Dispose();
                }
            };
            
            timer.Start();
        }

        public static void Pulse(Control control, int pulseCount = 3, int duration = 200)
        {
            Size originalSize = control.Size;
            int currentPulse = 0;
            
            WinFormsTimer timer = new WinFormsTimer();
            timer.Interval = 16;
            DateTime startTime = DateTime.Now;
            bool expanding = true;
            
            timer.Tick += (sender, e) =>
            {
                double elapsed = (DateTime.Now - startTime).TotalMilliseconds;
                double cycleProgress = (elapsed % duration) / duration;
                
                if (cycleProgress < 0.5)
                {
                    // التوسع
                    double progress = cycleProgress * 2;
                    int scaleAmount = (int)(10 * progress);
                    control.Size = new Size(
                        originalSize.Width + scaleAmount,
                        originalSize.Height + scaleAmount
                    );
                }
                else
                {
                    // الانكماش
                    double progress = (cycleProgress - 0.5) * 2;
                    int scaleAmount = (int)(10 * (1 - progress));
                    control.Size = new Size(
                        originalSize.Width + scaleAmount,
                        originalSize.Height + scaleAmount
                    );
                }
                
                if (elapsed >= duration)
                {
                    currentPulse++;
                    startTime = DateTime.Now;
                    
                    if (currentPulse >= pulseCount)
                    {
                        control.Size = originalSize;
                        timer.Stop();
                        timer.Dispose();
                    }
                }
            };
            
            timer.Start();
        }

        public static void Shake(Control control, int intensity = 5, int duration = 500)
        {
            Point originalLocation = control.Location;
            Random random = new Random();
            
            WinFormsTimer timer = new WinFormsTimer();
            timer.Interval = 50;
            DateTime startTime = DateTime.Now;
            
            timer.Tick += (sender, e) =>
            {
                double elapsed = (DateTime.Now - startTime).TotalMilliseconds;
                double progress = elapsed / duration;
                
                if (progress >= 1.0)
                {
                    control.Location = originalLocation;
                    timer.Stop();
                    timer.Dispose();
                    return;
                }
                
                // تقليل الشدة تدريجياً
                int currentIntensity = (int)(intensity * (1 - progress));
                
                int offsetX = random.Next(-currentIntensity, currentIntensity + 1);
                int offsetY = random.Next(-currentIntensity, currentIntensity + 1);
                
                control.Location = new Point(
                    originalLocation.X + offsetX,
                    originalLocation.Y + offsetY
                );
            };
            
            timer.Start();
        }

        public static void HighlightControl(Control control, Color highlightColor, int duration = 1000)
        {
            Color originalBackColor = control.BackColor;
            
            WinFormsTimer timer = new WinFormsTimer();
            timer.Interval = 16;
            DateTime startTime = DateTime.Now;
            
            timer.Tick += (sender, e) =>
            {
                double elapsed = (DateTime.Now - startTime).TotalMilliseconds;
                double progress = elapsed / duration;
                
                if (progress >= 1.0)
                {
                    control.BackColor = originalBackColor;
                    timer.Stop();
                    timer.Dispose();
                    return;
                }
                
                // تأثير النبض للإضاءة
                double intensity = Math.Sin(progress * Math.PI * 4) * 0.5 + 0.5;
                intensity *= (1 - progress); // تقليل الشدة تدريجياً
                
                int r = (int)(originalBackColor.R + (highlightColor.R - originalBackColor.R) * intensity);
                int g = (int)(originalBackColor.G + (highlightColor.G - originalBackColor.G) * intensity);
                int b = (int)(originalBackColor.B + (highlightColor.B - originalBackColor.B) * intensity);
                
                control.BackColor = Color.FromArgb(r, g, b);
            };
            
            timer.Start();
        }

        public static void AnimateFormLoad(Form form)
        {
            form.Opacity = 0;
            form.Show();
            
            WinFormsTimer timer = new WinFormsTimer();
            timer.Interval = 16;
            DateTime startTime = DateTime.Now;
            int duration = 400;
            
            timer.Tick += (sender, e) =>
            {
                double elapsed = (DateTime.Now - startTime).TotalMilliseconds;
                double progress = Math.Min(elapsed / duration, 1.0);
                
                // منحنى ease-out
                progress = 1 - Math.Pow(1 - progress, 3);
                
                form.Opacity = progress;
                
                if (progress >= 1.0)
                {
                    timer.Stop();
                    timer.Dispose();
                }
            };
            
            timer.Start();
        }
    }

    public enum SlideDirection
    {
        FromLeft,
        FromRight,
        FromTop,
        FromBottom
    }
}
