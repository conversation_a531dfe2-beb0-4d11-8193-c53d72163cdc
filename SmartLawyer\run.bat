@echo off
title Smart Lawyer - برنامج إدارة مكتب المحاماة
echo.
echo ========================================
echo    Smart Lawyer - سمارت لوير
echo    برنامج إدارة مكتب المحاماة
echo ========================================
echo    تطوير: Generation Five
echo    هاتف: +212 661 77 53 72
echo    الموقع: www.generationfive.net
echo ========================================
echo.
echo جاري تشغيل البرنامج...
echo.

REM التحقق من وجود .NET Runtime
dotnet --version >nul 2>&1
if errorlevel 1 (
    echo خطأ: .NET Runtime غير مثبت على النظام
    echo يرجى تحميل وتثبيت .NET 6.0 Runtime من:
    echo https://dotnet.microsoft.com/download/dotnet/6.0
    pause
    exit /b 1
)

REM إنشاء المجلدات المطلوبة
if not exist "Logs" mkdir Logs
if not exist "Backups" mkdir Backups
if not exist "Reports" mkdir Reports
if not exist "Temp" mkdir Temp
if not exist "Documents" mkdir Documents

REM تشغيل البرنامج
dotnet run

REM في حالة حدوث خطأ
if errorlevel 1 (
    echo.
    echo حدث خطأ أثناء تشغيل البرنامج
    echo يرجى التحقق من ملف السجل في مجلد Logs
    pause
)

echo.
echo تم إغلاق البرنامج
pause
