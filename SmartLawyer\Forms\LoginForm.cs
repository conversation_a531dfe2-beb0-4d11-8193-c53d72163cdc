using System;
using System.Data.SQLite;
using System.Drawing;
using System.Windows.Forms;
using SmartLawyer.Database;
using SmartLawyer.Localization;

namespace SmartLawyer.Forms
{
    public partial class LoginForm : Form
    {
        private Panel leftPanel;
        private Panel rightPanel;
        private TextBox txtUsername;
        private TextBox txtPassword;
        private Button btnLogin;
        private Label lblUsername;
        private Label lblPassword;
        private Label lblProgramName;
        private Label lblFeatures;
        private Label lblDeveloper;
        private Label lblContact;
        private PictureBox picLogo;
        private ComboBox cmbLanguage;

        public LoginForm()
        {
            InitializeComponent();
            SetupForm();
            LoadLanguage();
        }

        private void InitializeComponent()
        {
            this.SuspendLayout();
            
            // إعداد النافذة الرئيسية
            this.Text = "Smart Lawyer - تسجيل الدخول";
            this.Size = new Size(1000, 600);
            this.StartPosition = FormStartPosition.CenterScreen;
            this.FormBorderStyle = FormBorderStyle.FixedDialog;
            this.MaximizeBox = false;
            this.MinimizeBox = false;
            this.BackColor = Color.FromArgb(240, 240, 240);

            // اللوحة اليسرى - تسجيل الدخول
            leftPanel = new Panel
            {
                Size = new Size(500, 600),
                Location = new Point(0, 0),
                BackColor = Color.White,
                Padding = new Padding(50)
            };

            // اللوحة اليمنى - معلومات البرنامج
            rightPanel = new Panel
            {
                Size = new Size(500, 600),
                Location = new Point(500, 0),
                BackColor = Color.FromArgb(41, 128, 185),
                Padding = new Padding(30)
            };

            // عناصر تسجيل الدخول
            lblUsername = new Label
            {
                Text = "اسم المستخدم",
                Location = new Point(50, 150),
                Size = new Size(400, 30),
                Font = new Font("Segoe UI", 12, FontStyle.Bold),
                ForeColor = Color.FromArgb(52, 73, 94),
                TextAlign = ContentAlignment.MiddleRight
            };

            txtUsername = new TextBox
            {
                Location = new Point(50, 185),
                Size = new Size(400, 35),
                Font = new Font("Segoe UI", 12),
                RightToLeft = RightToLeft.Yes,
                BorderStyle = BorderStyle.FixedSingle
            };

            lblPassword = new Label
            {
                Text = "كلمة المرور",
                Location = new Point(50, 240),
                Size = new Size(400, 30),
                Font = new Font("Segoe UI", 12, FontStyle.Bold),
                ForeColor = Color.FromArgb(52, 73, 94),
                TextAlign = ContentAlignment.MiddleRight
            };

            txtPassword = new TextBox
            {
                Location = new Point(50, 275),
                Size = new Size(400, 35),
                Font = new Font("Segoe UI", 12),
                PasswordChar = '*',
                RightToLeft = RightToLeft.Yes,
                BorderStyle = BorderStyle.FixedSingle
            };

            btnLogin = new Button
            {
                Text = "دخول",
                Location = new Point(50, 340),
                Size = new Size(400, 45),
                Font = new Font("Segoe UI", 14, FontStyle.Bold),
                BackColor = Color.FromArgb(41, 128, 185),
                ForeColor = Color.White,
                FlatStyle = FlatStyle.Flat,
                Cursor = Cursors.Hand
            };
            btnLogin.FlatAppearance.BorderSize = 0;
            btnLogin.Click += BtnLogin_Click;

            // قائمة اللغة
            cmbLanguage = new ComboBox
            {
                Location = new Point(50, 400),
                Size = new Size(150, 30),
                Font = new Font("Segoe UI", 10),
                DropDownStyle = ComboBoxStyle.DropDownList
            };
            cmbLanguage.Items.Add("العربية");
            cmbLanguage.Items.Add("Français");
            cmbLanguage.SelectedIndex = 0;
            cmbLanguage.SelectedIndexChanged += CmbLanguage_SelectedIndexChanged;

            // شعار البرنامج
            picLogo = new PictureBox
            {
                Location = new Point(150, 50),
                Size = new Size(200, 100),
                SizeMode = PictureBoxSizeMode.Zoom,
                BackColor = Color.Transparent
            };
            // يمكن إضافة صورة الشعار هنا
            // picLogo.Image = Properties.Resources.Logo;

            // اسم البرنامج
            lblProgramName = new Label
            {
                Text = "سمارت لوير\nبرنامج إدارة مكتب المحاماة",
                Location = new Point(30, 170),
                Size = new Size(440, 60),
                Font = new Font("Segoe UI", 18, FontStyle.Bold),
                ForeColor = Color.White,
                TextAlign = ContentAlignment.MiddleCenter
            };

            // مميزات البرنامج
            lblFeatures = new Label
            {
                Text = "• إدارة شاملة للموكلين والملفات\n• متابعة الجلسات والمواعيد\n• نظام إشعارات ذكي\n• تقارير مفصلة وإحصائيات\n• أرشفة إلكترونية متقدمة",
                Location = new Point(30, 250),
                Size = new Size(440, 150),
                Font = new Font("Segoe UI", 12),
                ForeColor = Color.White,
                TextAlign = ContentAlignment.MiddleRight
            };

            // معلومات المطور
            lblDeveloper = new Label
            {
                Text = "تطوير: Generation Five",
                Location = new Point(30, 420),
                Size = new Size(440, 30),
                Font = new Font("Segoe UI", 12, FontStyle.Bold),
                ForeColor = Color.White,
                TextAlign = ContentAlignment.MiddleCenter
            };

            lblContact = new Label
            {
                Text = "هاتف: +212 661 77 53 72\nالموقع: www.generationfive.net",
                Location = new Point(30, 460),
                Size = new Size(440, 50),
                Font = new Font("Segoe UI", 10),
                ForeColor = Color.White,
                TextAlign = ContentAlignment.MiddleCenter
            };

            // إضافة العناصر إلى اللوحات
            leftPanel.Controls.Add(lblUsername);
            leftPanel.Controls.Add(txtUsername);
            leftPanel.Controls.Add(lblPassword);
            leftPanel.Controls.Add(txtPassword);
            leftPanel.Controls.Add(btnLogin);
            leftPanel.Controls.Add(cmbLanguage);

            rightPanel.Controls.Add(picLogo);
            rightPanel.Controls.Add(lblProgramName);
            rightPanel.Controls.Add(lblFeatures);
            rightPanel.Controls.Add(lblDeveloper);
            rightPanel.Controls.Add(lblContact);

            // إضافة اللوحات إلى النافذة
            this.Controls.Add(leftPanel);
            this.Controls.Add(rightPanel);

            this.ResumeLayout(false);
        }

        private void SetupForm()
        {
            // تطبيق تأثيرات بصرية
            this.SetStyle(ControlStyles.AllPaintingInWmPaint | ControlStyles.UserPaint | ControlStyles.DoubleBuffer, true);
            
            // إعداد الأحداث
            txtPassword.KeyPress += TxtPassword_KeyPress;
            txtUsername.KeyPress += TxtUsername_KeyPress;
            
            // تركيز على حقل اسم المستخدم
            txtUsername.Focus();
        }

        private void LoadLanguage()
        {
            var lang = LanguageManager.Instance;
            
            this.Text = lang.GetText("Login");
            lblUsername.Text = lang.GetText("Username");
            lblPassword.Text = lang.GetText("Password");
            btnLogin.Text = lang.GetText("LoginButton");
            lblProgramName.Text = lang.GetText("ProgramName");
            lblFeatures.Text = lang.GetText("ProgramFeatures");
            lblDeveloper.Text = lang.GetText("Developer");
            lblContact.Text = lang.GetText("Contact");
        }

        private void CmbLanguage_SelectedIndexChanged(object sender, EventArgs e)
        {
            string selectedLanguage = cmbLanguage.SelectedIndex == 0 ? "ar" : "fr";
            LanguageManager.Instance.CurrentLanguage = selectedLanguage;
            LoadLanguage();
            
            // تحديث اتجاه النص
            if (selectedLanguage == "ar")
            {
                txtUsername.RightToLeft = RightToLeft.Yes;
                txtPassword.RightToLeft = RightToLeft.Yes;
                lblUsername.TextAlign = ContentAlignment.MiddleRight;
                lblPassword.TextAlign = ContentAlignment.MiddleRight;
            }
            else
            {
                txtUsername.RightToLeft = RightToLeft.No;
                txtPassword.RightToLeft = RightToLeft.No;
                lblUsername.TextAlign = ContentAlignment.MiddleLeft;
                lblPassword.TextAlign = ContentAlignment.MiddleLeft;
            }
        }

        private void TxtUsername_KeyPress(object sender, KeyPressEventArgs e)
        {
            if (e.KeyChar == (char)Keys.Enter)
            {
                txtPassword.Focus();
                e.Handled = true;
            }
        }

        private void TxtPassword_KeyPress(object sender, KeyPressEventArgs e)
        {
            if (e.KeyChar == (char)Keys.Enter)
            {
                BtnLogin_Click(sender, e);
                e.Handled = true;
            }
        }

        private void BtnLogin_Click(object sender, EventArgs e)
        {
            if (string.IsNullOrWhiteSpace(txtUsername.Text) || string.IsNullOrWhiteSpace(txtPassword.Text))
            {
                MessageBox.Show(LanguageManager.Instance.GetText("RequiredField"), 
                               LanguageManager.Instance.GetText("ValidationError"), 
                               MessageBoxButtons.OK, MessageBoxIcon.Warning);
                return;
            }

            if (ValidateUser(txtUsername.Text, txtPassword.Text))
            {
                this.DialogResult = DialogResult.OK;
                this.Close();
            }
            else
            {
                MessageBox.Show(LanguageManager.Instance.GetText("InvalidCredentials"), 
                               LanguageManager.Instance.GetText("LoginError"), 
                               MessageBoxButtons.OK, MessageBoxIcon.Error);
                txtPassword.Clear();
                txtUsername.Focus();
            }
        }

        private bool ValidateUser(string username, string password)
        {
            try
            {
                using (var connection = DatabaseManager.Instance.GetConnection())
                {
                    connection.Open();
                    string query = "SELECT COUNT(*) FROM Users WHERE Username = @username AND Password = @password AND IsActive = 1";
                    using (var command = new SQLiteCommand(query, connection))
                    {
                        command.Parameters.AddWithValue("@username", username);
                        command.Parameters.AddWithValue("@password", password);
                        
                        int count = Convert.ToInt32(command.ExecuteScalar());
                        
                        if (count > 0)
                        {
                            // تحديث تاريخ آخر دخول
                            string updateQuery = "UPDATE Users SET LastLogin = @lastLogin WHERE Username = @username";
                            using (var updateCommand = new SQLiteCommand(updateQuery, connection))
                            {
                                updateCommand.Parameters.AddWithValue("@lastLogin", DateTime.Now);
                                updateCommand.Parameters.AddWithValue("@username", username);
                                updateCommand.ExecuteNonQuery();
                            }
                            return true;
                        }
                    }
                }
            }
            catch (Exception ex)
            {
                MessageBox.Show($"خطأ في الاتصال بقاعدة البيانات: {ex.Message}", "خطأ", MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
            
            return false;
        }
    }
}
