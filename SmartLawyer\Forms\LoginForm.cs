using System;
using System.Data.SQLite;
using System.Drawing;
using System.Drawing.Drawing2D;
using System.Windows.Forms;
using SmartLawyer.Database;
using SmartLawyer.Localization;
using SmartLawyer.UI;

namespace SmartLawyer.Forms
{
    public partial class LoginForm : Form
    {
        private LegalCard leftPanel;
        private LegalCard rightPanel;
        private ModernTextBox txtUsername;
        private ModernTextBox txtPassword;
        private LegalButton btnLogin;
        private Label lblUsername;
        private Label lblPassword;
        private Label lblProgramName;
        private Label lblFeatures;
        private Label lblDeveloper;
        private Label lblContact;
        private PictureBox picLogo;
        private ComboBox cmbLanguage;
        private ModernButton btnLanguageToggle;

        public LoginForm()
        {
            // إعدادات لتقليل الارتجاف
            SetStyle(ControlStyles.AllPaintingInWmPaint |
                     ControlStyles.UserPaint |
                     ControlStyles.DoubleBuffer |
                     ControlStyles.ResizeRedraw, true);

            InitializeComponent();
            SetupForm();
            LoadLanguage();
            ApplyLegalTheme();

            // تطبيق إعدادات اللغة
            LanguageManager.Instance.ApplyLanguageToForm(this);

            // إضافة تأثيرات تفاعلية
            SetupInteractiveEffects();

            // تطبيق انتقالات الظهور
            this.Load += LoginForm_Load;
        }

        private void InitializeComponent()
        {
            this.SuspendLayout();

            // إعداد النافذة الرئيسية
            this.Text = "Smart Lawyer - تسجيل الدخول";
            this.Size = new Size(1000, 600);
            this.StartPosition = FormStartPosition.CenterScreen;
            this.FormBorderStyle = FormBorderStyle.None;
            this.MaximizeBox = false;
            this.MinimizeBox = false;
            this.BackColor = Color.LightGray;

            // اللوحة اليسرى - تسجيل الدخول (تصميم مبسط)
            leftPanel = new LegalCard
            {
                Size = new Size(500, 600),
                Location = new Point(0, 0),
                BackColor = Color.White,
                Padding = new Padding(60),
                BorderRadius = 0,
                HasElevation = false,
                HasGradient = false
            };

            // اللوحة اليمنى - معلومات البرنامج (تصميم مبسط)
            rightPanel = new LegalCard
            {
                Size = new Size(500, 600),
                Location = new Point(500, 0),
                BackColor = ThemeManager.Instance.GetPrimaryColor(),
                Padding = new Padding(40),
                BorderRadius = 0,
                HasElevation = false,
                HasGradient = false
            };

            // عناصر تسجيل الدخول
            lblUsername = new Label
            {
                Text = "اسم المستخدم",
                Location = new Point(50, 150),
                Size = new Size(400, 30),
                Font = new Font("Segoe UI", 12, FontStyle.Bold),
                ForeColor = ThemeManager.Instance.GetTextColor(),
                TextAlign = ContentAlignment.MiddleRight,
                BackColor = Color.Transparent
            };

            txtUsername = new ModernTextBox
            {
                Location = new Point(50, 185),
                Size = new Size(400, 45),
                Font = new Font("Segoe UI", 12),
                PlaceholderText = "أدخل اسم المستخدم",
                BackColor = Color.White,
                ForeColor = Color.Black,
                BorderColor = ThemeManager.Instance.GetBorderColor()
            };

            lblPassword = new Label
            {
                Text = "كلمة المرور",
                Location = new Point(50, 250),
                Size = new Size(400, 30),
                Font = new Font("Segoe UI", 12, FontStyle.Bold),
                ForeColor = ThemeManager.Instance.GetTextColor(),
                TextAlign = ContentAlignment.MiddleRight,
                BackColor = Color.Transparent
            };

            txtPassword = new ModernTextBox
            {
                Location = new Point(50, 285),
                Size = new Size(400, 45),
                Font = new Font("Segoe UI", 12),
                PlaceholderText = "أدخل كلمة المرور",
                BackColor = Color.White,
                ForeColor = Color.Black,
                BorderColor = ThemeManager.Instance.GetBorderColor(),
                UseSystemPasswordChar = true
            };

            btnLogin = new LegalButton
            {
                Text = "دخول",
                Location = new Point(50, 380),
                Size = new Size(380, 55),
                Font = new Font("Segoe UI", 14, FontStyle.Bold),
                BackColor = ThemeManager.Instance.GetPrimaryColor(),
                ForeColor = ThemeManager.Instance.GetTextOnPrimaryColor(),
                BorderRadius = 12,
                HasElevation = true,
                Elevation = 4,
                IconName = "login",
                IconSize = 24
            };
            btnLogin.Click += BtnLogin_Click;

            // زر تبديل اللغة
            btnLanguageToggle = new ModernButton
            {
                Text = "FR",
                Location = new Point(50, 430),
                Size = new Size(80, 35),
                Font = new Font("Segoe UI", 10, FontStyle.Bold),
                BackColor = ThemeManager.Instance.GetTextSecondaryColor(),
                ForeColor = Color.White,
                BorderRadius = 8
            };
            btnLanguageToggle.Click += BtnLanguageToggle_Click;

            // شعار البرنامج
            picLogo = new PictureBox
            {
                Location = new Point(150, 50),
                Size = new Size(200, 100),
                SizeMode = PictureBoxSizeMode.Zoom,
                BackColor = Color.Transparent
            };
            // يمكن إضافة صورة الشعار هنا
            // picLogo.Image = Properties.Resources.Logo;

            // اسم البرنامج
            lblProgramName = new Label
            {
                Text = "سمارت لوير\nبرنامج إدارة مكتب المحاماة",
                Location = new Point(30, 170),
                Size = new Size(440, 60),
                Font = new Font("Segoe UI", 18, FontStyle.Bold),
                ForeColor = Color.White,
                TextAlign = ContentAlignment.MiddleCenter
            };

            // مميزات البرنامج
            lblFeatures = new Label
            {
                Text = "• إدارة شاملة للموكلين والملفات\n• متابعة الجلسات والمواعيد\n• نظام إشعارات ذكي\n• تقارير مفصلة وإحصائيات\n• أرشفة إلكترونية متقدمة",
                Location = new Point(30, 250),
                Size = new Size(440, 150),
                Font = new Font("Segoe UI", 12),
                ForeColor = Color.White,
                TextAlign = ContentAlignment.MiddleRight
            };

            // معلومات المطور
            lblDeveloper = new Label
            {
                Text = "تطوير: Generation Five",
                Location = new Point(30, 420),
                Size = new Size(440, 30),
                Font = new Font("Segoe UI", 12, FontStyle.Bold),
                ForeColor = Color.White,
                TextAlign = ContentAlignment.MiddleCenter
            };

            lblContact = new Label
            {
                Text = "هاتف: +212 661 77 53 72\nالموقع: www.generationfive.net",
                Location = new Point(30, 460),
                Size = new Size(440, 50),
                Font = new Font("Segoe UI", 10),
                ForeColor = Color.White,
                TextAlign = ContentAlignment.MiddleCenter
            };

            // إضافة العناصر إلى اللوحات
            leftPanel.Controls.Add(lblUsername);
            leftPanel.Controls.Add(txtUsername);
            leftPanel.Controls.Add(lblPassword);
            leftPanel.Controls.Add(txtPassword);
            leftPanel.Controls.Add(btnLogin);
            leftPanel.Controls.Add(btnLanguageToggle);

            rightPanel.Controls.Add(picLogo);
            rightPanel.Controls.Add(lblProgramName);
            rightPanel.Controls.Add(lblFeatures);
            rightPanel.Controls.Add(lblDeveloper);
            rightPanel.Controls.Add(lblContact);

            // إضافة اللوحات إلى النافذة
            this.Controls.Add(leftPanel);
            this.Controls.Add(rightPanel);

            this.ResumeLayout(false);
        }

        private void SetupForm()
        {
            // تطبيق تأثيرات بصرية
            this.SetStyle(ControlStyles.AllPaintingInWmPaint | ControlStyles.UserPaint | ControlStyles.DoubleBuffer, true);

            // إعداد الأحداث
            this.Shown += LoginForm_Shown;

            // تركيز على حقل اسم المستخدم
            this.Activated += (s, e) => txtUsername.Focus();
        }



        private void LoginForm_Shown(object sender, EventArgs e)
        {
            // تطبيق انتقالات للعناصر
            AnimationManager.SlideIn(leftPanel, SlideDirection.FromLeft, 600);
            AnimationManager.SlideIn(rightPanel, SlideDirection.FromRight, 600);

            // تأخير ظهور العناصر الداخلية
            System.Windows.Forms.Timer delayTimer = new System.Windows.Forms.Timer();
            delayTimer.Interval = 300;
            delayTimer.Tick += (s, ev) =>
            {
                AnimationManager.FadeIn(lblUsername, 400);
                AnimationManager.FadeIn(txtUsername, 500);
                AnimationManager.FadeIn(lblPassword, 600);
                AnimationManager.FadeIn(txtPassword, 700);
                AnimationManager.FadeIn(btnLogin, 800);
                AnimationManager.FadeIn(btnLanguageToggle, 900);

                delayTimer.Stop();
                delayTimer.Dispose();
            };
            delayTimer.Start();
        }

        private void ApplyTheme()
        {
            var theme = ThemeManager.Instance;

            // تطبيق ألوان الثيم
            this.BackColor = theme.GetBackgroundColor();
            leftPanel.BackColor = theme.GetSurfaceColor();
            rightPanel.BackColor = theme.GetPrimaryColor();

            lblUsername.ForeColor = theme.GetTextColor();
            lblPassword.ForeColor = theme.GetTextColor();

            btnLogin.BackColor = theme.GetPrimaryColor();

            // تطبيق الخطوط
            var currentLang = LanguageManager.Instance.CurrentLanguageInfo;
            Font newFont = new Font(currentLang.FontFamily, 12);

            lblUsername.Font = new Font(currentLang.FontFamily, 12, FontStyle.Bold);
            lblPassword.Font = new Font(currentLang.FontFamily, 12, FontStyle.Bold);
            txtUsername.Font = newFont;
            txtPassword.Font = newFont;
        }

        private void LoadLanguage()
        {
            var lang = LanguageManager.Instance;
            
            this.Text = lang.GetText("Login");
            lblUsername.Text = lang.GetText("Username");
            lblPassword.Text = lang.GetText("Password");
            btnLogin.Text = lang.GetText("LoginButton");
            lblProgramName.Text = lang.GetText("ProgramName");
            lblFeatures.Text = lang.GetText("ProgramFeatures");
            lblDeveloper.Text = lang.GetText("Developer");
            lblContact.Text = lang.GetText("Contact");
        }

        private void BtnLanguageToggle_Click(object sender, EventArgs e)
        {
            // تبديل اللغة
            string newLanguage = LanguageManager.Instance.CurrentLanguage == "ar" ? "fr" : "ar";
            LanguageManager.Instance.CurrentLanguage = newLanguage;

            // تحديث نص الزر
            btnLanguageToggle.Text = newLanguage == "ar" ? "FR" : "ع";

            // تطبيق التغييرات
            LoadLanguage();
            ApplyTheme();
            LanguageManager.Instance.ApplyLanguageToForm(this);

            // تأثير بصري للتبديل
            AnimationManager.Pulse(btnLanguageToggle, 2, 150);

            // تحديث النصوص التوضيحية
            txtUsername.PlaceholderText = LanguageManager.Instance.GetText("Username");
            txtPassword.PlaceholderText = LanguageManager.Instance.GetText("Password");
        }

        private void TxtUsername_KeyPress(object sender, KeyPressEventArgs e)
        {
            if (e.KeyChar == (char)Keys.Enter)
            {
                txtPassword.Focus();
                e.Handled = true;
            }
        }

        private void TxtPassword_KeyPress(object sender, KeyPressEventArgs e)
        {
            if (e.KeyChar == (char)Keys.Enter)
            {
                BtnLogin_Click(sender, e);
                e.Handled = true;
            }
        }

        private void BtnLogin_Click(object sender, EventArgs e)
        {
            // تأثير بصري للزر
            AnimationManager.Pulse(btnLogin, 1, 100);

            if (string.IsNullOrWhiteSpace(txtUsername.Text) || string.IsNullOrWhiteSpace(txtPassword.Text))
            {
                // تأثير اهتزاز للحقول الفارغة
                if (string.IsNullOrWhiteSpace(txtUsername.Text))
                    AnimationManager.Shake(txtUsername, 3, 300);
                if (string.IsNullOrWhiteSpace(txtPassword.Text))
                    AnimationManager.Shake(txtPassword, 3, 300);

                MessageBox.Show(LanguageManager.Instance.GetText("RequiredField"),
                               LanguageManager.Instance.GetText("ValidationError"),
                               MessageBoxButtons.OK, MessageBoxIcon.Warning);
                return;
            }

            // تأثير تحميل
            btnLogin.Text = LanguageManager.Instance.CurrentLanguage == "ar" ? "جاري التحقق..." : "Vérification...";
            btnLogin.Enabled = false;

            // محاكاة تأخير التحقق
            System.Windows.Forms.Timer verificationTimer = new System.Windows.Forms.Timer();
            verificationTimer.Interval = 1000;
            verificationTimer.Tick += (s, ev) =>
            {
                verificationTimer.Stop();
                verificationTimer.Dispose();

                btnLogin.Text = LanguageManager.Instance.GetText("LoginButton");
                btnLogin.Enabled = true;

                if (ValidateUser(txtUsername.Text, txtPassword.Text))
                {
                    // تأثير نجاح
                    AnimationManager.HighlightControl(btnLogin, Color.FromArgb(46, 204, 113), 500);

                    System.Windows.Forms.Timer successTimer = new System.Windows.Forms.Timer();
                    successTimer.Interval = 500;
                    successTimer.Tick += (s2, ev2) =>
                    {
                        successTimer.Stop();
                        successTimer.Dispose();
                        this.DialogResult = DialogResult.OK;
                        this.Close();
                    };
                    successTimer.Start();
                }
                else
                {
                    // تأثير خطأ
                    AnimationManager.HighlightControl(btnLogin, Color.FromArgb(231, 76, 60), 500);
                    AnimationManager.Shake(leftPanel, 5, 400);

                    MessageBox.Show(LanguageManager.Instance.GetText("InvalidCredentials"),
                                   LanguageManager.Instance.GetText("LoginError"),
                                   MessageBoxButtons.OK, MessageBoxIcon.Error);
                    txtPassword.Text = "";
                    txtUsername.Focus();
                }
            };
            verificationTimer.Start();
        }

        private bool ValidateUser(string username, string password)
        {
            try
            {
                using (var connection = DatabaseManager.Instance.GetConnection())
                {
                    connection.Open();
                    string query = "SELECT COUNT(*) FROM Users WHERE Username = @username AND Password = @password AND IsActive = 1";
                    using (var command = new SQLiteCommand(query, connection))
                    {
                        command.Parameters.AddWithValue("@username", username);
                        command.Parameters.AddWithValue("@password", password);
                        
                        int count = Convert.ToInt32(command.ExecuteScalar());
                        
                        if (count > 0)
                        {
                            // تحديث تاريخ آخر دخول
                            string updateQuery = "UPDATE Users SET LastLogin = @lastLogin WHERE Username = @username";
                            using (var updateCommand = new SQLiteCommand(updateQuery, connection))
                            {
                                updateCommand.Parameters.AddWithValue("@lastLogin", DateTime.Now);
                                updateCommand.Parameters.AddWithValue("@username", username);
                                updateCommand.ExecuteNonQuery();
                            }
                            return true;
                        }
                    }
                }
            }
            catch (Exception ex)
            {
                MessageBox.Show($"خطأ في الاتصال بقاعدة البيانات: {ex.Message}", "خطأ", MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
            
            return false;
        }
        private void ApplyLegalTheme()
        {
            var theme = ThemeManager.Instance;

            // تطبيق ألوان الثيم القانوني
            this.BackColor = theme.GetBackgroundColor();

            // تحديث ألوان النصوص بالألوان القانونية
            lblUsername.ForeColor = theme.GetTextColor();
            lblPassword.ForeColor = theme.GetTextColor();

            lblProgramName.ForeColor = theme.GetTextOnPrimaryColor();
            lblFeatures.ForeColor = theme.GetTextOnPrimaryColor();
            lblDeveloper.ForeColor = theme.GetTextOnPrimaryColor();
            lblContact.ForeColor = theme.GetTextOnPrimaryColor();

            // تطبيق الخطوط القانونية الاحترافية
            var currentLang = LanguageManager.Instance.CurrentLanguageInfo;
            Font legalFont = new Font("Segoe UI", 12, FontStyle.Regular);
            Font legalHeaderFont = new Font("Segoe UI", 16, FontStyle.Bold);

            lblProgramName.Font = legalHeaderFont;
            lblUsername.Font = new Font("Segoe UI", 12, FontStyle.Bold);
            lblPassword.Font = new Font("Segoe UI", 12, FontStyle.Bold);
            txtUsername.Font = legalFont;
            txtPassword.Font = legalFont;
            btnLogin.Font = new Font("Segoe UI", 14, FontStyle.Bold);

            // إضافة تأثيرات ذهبية للعناوين
            lblProgramName.ForeColor = theme.GetAccentColor();
        }

        private void SetupInteractiveEffects()
        {
            // تأثيرات بسيطة للأزرار
            btnLogin.MouseEnter += (s, e) =>
            {
                btnLogin.BackColor = ControlPaint.Light(ThemeManager.Instance.GetPrimaryColor(), 0.1f);
            };

            btnLogin.MouseLeave += (s, e) =>
            {
                btnLogin.BackColor = ThemeManager.Instance.GetPrimaryColor();
            };
        }

        private void LoginForm_Load(object sender, EventArgs e)
        {
            // إظهار بسيط بدون تأثيرات
            leftPanel.Visible = true;
            rightPanel.Visible = true;

            // التركيز على حقل اسم المستخدم
            txtUsername.Focus();
        }
    }
}
