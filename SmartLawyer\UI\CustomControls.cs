using System;
using System.Drawing;
using System.Drawing.Drawing2D;
using System.Windows.Forms;

namespace SmartLawyer.UI
{
    public class ModernButton : Button
    {
        private Color _hoverColor;
        private Color _pressedColor;
        private int _borderRadius = 8;
        private bool _isHovered = false;
        private bool _isPressed = false;

        public ModernButton()
        {
            SetStyle(ControlStyles.AllPaintingInWmPaint | 
                     ControlStyles.UserPaint | 
                     ControlStyles.DoubleBuffer | 
                     ControlStyles.ResizeRedraw, true);
            
            FlatStyle = FlatStyle.Flat;
            FlatAppearance.BorderSize = 0;
            Font = new Font("Segoe UI", 10, FontStyle.Bold);
            Cursor = Cursors.Hand;
            
            UpdateColors();
        }

        public int BorderRadius
        {
            get { return _borderRadius; }
            set { _borderRadius = value; Invalidate(); }
        }

        private void UpdateColors()
        {
            var theme = ThemeManager.Instance;
            _hoverColor = ControlPaint.Light(BackColor, 0.2f);
            _pressedColor = ControlPaint.Dark(BackColor, 0.1f);
        }

        protected override void OnPaint(PaintEventArgs e)
        {
            Graphics g = e.Graphics;
            g.SmoothingMode = SmoothingMode.AntiAlias;

            Rectangle rect = new Rectangle(0, 0, Width - 1, Height - 1);
            Color currentColor = BackColor;

            if (_isPressed)
                currentColor = _pressedColor;
            else if (_isHovered)
                currentColor = _hoverColor;

            // رسم الخلفية مع الحواف المدورة
            using (GraphicsPath path = GetRoundedRectangle(rect, _borderRadius))
            {
                using (SolidBrush brush = new SolidBrush(currentColor))
                {
                    g.FillPath(brush, path);
                }

                // إضافة ظل خفيف
                using (Pen shadowPen = new Pen(Color.FromArgb(50, 0, 0, 0), 1))
                {
                    Rectangle shadowRect = new Rectangle(rect.X + 1, rect.Y + 1, rect.Width, rect.Height);
                    using (GraphicsPath shadowPath = GetRoundedRectangle(shadowRect, _borderRadius))
                    {
                        g.DrawPath(shadowPen, shadowPath);
                    }
                }
            }

            // رسم النص
            TextRenderer.DrawText(g, Text, Font, rect, ForeColor, 
                TextFormatFlags.HorizontalCenter | TextFormatFlags.VerticalCenter);
        }

        private GraphicsPath GetRoundedRectangle(Rectangle rect, int radius)
        {
            GraphicsPath path = new GraphicsPath();
            path.AddArc(rect.X, rect.Y, radius, radius, 180, 90);
            path.AddArc(rect.X + rect.Width - radius, rect.Y, radius, radius, 270, 90);
            path.AddArc(rect.X + rect.Width - radius, rect.Y + rect.Height - radius, radius, radius, 0, 90);
            path.AddArc(rect.X, rect.Y + rect.Height - radius, radius, radius, 90, 90);
            path.CloseAllFigures();
            return path;
        }

        protected override void OnMouseEnter(EventArgs e)
        {
            _isHovered = true;
            Invalidate();
            base.OnMouseEnter(e);
        }

        protected override void OnMouseLeave(EventArgs e)
        {
            _isHovered = false;
            Invalidate();
            base.OnMouseLeave(e);
        }

        protected override void OnMouseDown(MouseEventArgs e)
        {
            _isPressed = true;
            Invalidate();
            base.OnMouseDown(e);
        }

        protected override void OnMouseUp(MouseEventArgs e)
        {
            _isPressed = false;
            Invalidate();
            base.OnMouseUp(e);
        }
    }

    public class ModernTextBox : UserControl
    {
        private TextBox _textBox;
        private Label _placeholder;
        private Panel _borderPanel;
        private bool _isFocused = false;

        public ModernTextBox()
        {
            SetupControls();
            SetStyle(ControlStyles.AllPaintingInWmPaint | 
                     ControlStyles.UserPaint | 
                     ControlStyles.DoubleBuffer, true);
        }

        private void SetupControls()
        {
            Size = new Size(200, 40);
            
            _borderPanel = new Panel
            {
                Dock = DockStyle.Fill,
                BackColor = ThemeManager.Instance.GetSurfaceColor(),
                Padding = new Padding(12, 8, 12, 8)
            };

            _textBox = new TextBox
            {
                BorderStyle = BorderStyle.None,
                Dock = DockStyle.Fill,
                Font = new Font("Segoe UI", 10),
                BackColor = ThemeManager.Instance.GetSurfaceColor(),
                ForeColor = ThemeManager.Instance.GetTextColor()
            };

            _placeholder = new Label
            {
                Text = "Enter text...",
                ForeColor = ThemeManager.Instance.GetSecondaryTextColor(),
                Font = new Font("Segoe UI", 10),
                BackColor = Color.Transparent,
                AutoSize = false,
                Dock = DockStyle.Fill,
                TextAlign = ContentAlignment.MiddleLeft
            };

            _borderPanel.Controls.Add(_textBox);
            _borderPanel.Controls.Add(_placeholder);
            Controls.Add(_borderPanel);

            _textBox.TextChanged += TextBox_TextChanged;
            _textBox.Enter += TextBox_Enter;
            _textBox.Leave += TextBox_Leave;
            _placeholder.Click += (s, e) => _textBox.Focus();

            UpdatePlaceholderVisibility();
        }

        public string PlaceholderText
        {
            get { return _placeholder.Text; }
            set { _placeholder.Text = value; }
        }

        public override string Text
        {
            get { return _textBox.Text; }
            set { _textBox.Text = value; UpdatePlaceholderVisibility(); }
        }

        public new Font Font
        {
            get { return _textBox.Font; }
            set { _textBox.Font = value; _placeholder.Font = value; }
        }

        private void TextBox_TextChanged(object sender, EventArgs e)
        {
            UpdatePlaceholderVisibility();
            OnTextChanged(e);
        }

        private void TextBox_Enter(object sender, EventArgs e)
        {
            _isFocused = true;
            UpdateBorderColor();
        }

        private void TextBox_Leave(object sender, EventArgs e)
        {
            _isFocused = false;
            UpdateBorderColor();
        }

        private void UpdatePlaceholderVisibility()
        {
            _placeholder.Visible = string.IsNullOrEmpty(_textBox.Text);
        }

        private void UpdateBorderColor()
        {
            Color borderColor = _isFocused ? 
                ThemeManager.Instance.GetPrimaryColor() : 
                ThemeManager.Instance.GetBorderColor();
            
            _borderPanel.BackColor = ThemeManager.Instance.GetSurfaceColor();
        }

        protected override void OnPaint(PaintEventArgs e)
        {
            Graphics g = e.Graphics;
            g.SmoothingMode = SmoothingMode.AntiAlias;

            Rectangle rect = new Rectangle(0, 0, Width - 1, Height - 1);
            Color borderColor = _isFocused ? 
                ThemeManager.Instance.GetPrimaryColor() : 
                ThemeManager.Instance.GetBorderColor();

            using (GraphicsPath path = GetRoundedRectangle(rect, 6))
            {
                using (Pen borderPen = new Pen(borderColor, _isFocused ? 2 : 1))
                {
                    g.DrawPath(borderPen, path);
                }
            }
        }

        private GraphicsPath GetRoundedRectangle(Rectangle rect, int radius)
        {
            GraphicsPath path = new GraphicsPath();
            path.AddArc(rect.X, rect.Y, radius, radius, 180, 90);
            path.AddArc(rect.X + rect.Width - radius, rect.Y, radius, radius, 270, 90);
            path.AddArc(rect.X + rect.Width - radius, rect.Y + rect.Height - radius, radius, radius, 0, 90);
            path.AddArc(rect.X, rect.Y + rect.Height - radius, radius, radius, 90, 90);
            path.CloseAllFigures();
            return path;
        }
    }

    public class ModernPanel : Panel
    {
        private int _borderRadius = 10;
        private Color _shadowColor = Color.FromArgb(50, 0, 0, 0);
        private bool _hasShadow = true;

        public ModernPanel()
        {
            SetStyle(ControlStyles.AllPaintingInWmPaint | 
                     ControlStyles.UserPaint | 
                     ControlStyles.DoubleBuffer | 
                     ControlStyles.ResizeRedraw, true);
            
            BackColor = ThemeManager.Instance.GetSurfaceColor();
            Padding = new Padding(15);
        }

        public int BorderRadius
        {
            get { return _borderRadius; }
            set { _borderRadius = value; Invalidate(); }
        }

        public bool HasShadow
        {
            get { return _hasShadow; }
            set { _hasShadow = value; Invalidate(); }
        }

        protected override void OnPaint(PaintEventArgs e)
        {
            Graphics g = e.Graphics;
            g.SmoothingMode = SmoothingMode.AntiAlias;

            Rectangle rect = new Rectangle(0, 0, Width - 1, Height - 1);

            // رسم الظل
            if (_hasShadow)
            {
                Rectangle shadowRect = new Rectangle(rect.X + 3, rect.Y + 3, rect.Width, rect.Height);
                using (GraphicsPath shadowPath = GetRoundedRectangle(shadowRect, _borderRadius))
                {
                    using (SolidBrush shadowBrush = new SolidBrush(_shadowColor))
                    {
                        g.FillPath(shadowBrush, shadowPath);
                    }
                }
            }

            // رسم الخلفية
            using (GraphicsPath path = GetRoundedRectangle(rect, _borderRadius))
            {
                using (SolidBrush brush = new SolidBrush(BackColor))
                {
                    g.FillPath(brush, path);
                }

                // رسم الحدود
                using (Pen borderPen = new Pen(ThemeManager.Instance.GetBorderColor(), 1))
                {
                    g.DrawPath(borderPen, path);
                }
            }
        }

        private GraphicsPath GetRoundedRectangle(Rectangle rect, int radius)
        {
            GraphicsPath path = new GraphicsPath();

            // التحقق من صحة المعاملات
            if (radius <= 0 || rect.Width <= 0 || rect.Height <= 0)
            {
                path.AddRectangle(rect);
                return path;
            }

            // التأكد من أن نصف القطر لا يتجاوز نصف أبعاد المستطيل
            int maxRadius = Math.Min(rect.Width / 2, rect.Height / 2);
            if (radius > maxRadius)
                radius = maxRadius;

            // إذا كان نصف القطر صغير جداً، ارسم مستطيل عادي
            if (radius < 2)
            {
                path.AddRectangle(rect);
                return path;
            }

            try
            {
                path.AddArc(rect.X, rect.Y, radius * 2, radius * 2, 180, 90);
                path.AddArc(rect.X + rect.Width - radius * 2, rect.Y, radius * 2, radius * 2, 270, 90);
                path.AddArc(rect.X + rect.Width - radius * 2, rect.Y + rect.Height - radius * 2, radius * 2, radius * 2, 0, 90);
                path.AddArc(rect.X, rect.Y + rect.Height - radius * 2, radius * 2, radius * 2, 90, 90);
                path.CloseAllFigures();
            }
            catch
            {
                // في حالة حدوث خطأ، ارسم مستطيل عادي
                path.Reset();
                path.AddRectangle(rect);
            }

            return path;
        }
    }
}
