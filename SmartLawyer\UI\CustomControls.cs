using System;
using System.Drawing;
using System.Drawing.Drawing2D;
using System.Windows.Forms;
using System.ComponentModel;

namespace SmartLawyer.UI
{
    public class ModernButton : Button
    {
        private Color _hoverColor;
        private Color _pressedColor;
        private int _borderRadius = 8;
        private bool _isHovered = false;
        private bool _isPressed = false;

        public ModernButton()
        {
            SetStyle(ControlStyles.AllPaintingInWmPaint | 
                     ControlStyles.UserPaint | 
                     ControlStyles.DoubleBuffer | 
                     ControlStyles.ResizeRedraw, true);
            
            FlatStyle = FlatStyle.Flat;
            FlatAppearance.BorderSize = 0;
            Font = new Font("Segoe UI", 10, FontStyle.Bold);
            Cursor = Cursors.Hand;
            
            UpdateColors();
        }

        public int BorderRadius
        {
            get { return _borderRadius; }
            set { _borderRadius = value; Invalidate(); }
        }

        private void UpdateColors()
        {
            var theme = ThemeManager.Instance;

            // ألوان أنيقة للتفاعل
            if (BackColor == Color.Transparent || BackColor == Color.Empty)
            {
                _hoverColor = Color.FromArgb(30, theme.GetAccentColor());
                _pressedColor = Color.FromArgb(50, theme.GetAccentColor());
            }
            else
            {
                _hoverColor = ControlPaint.Light(BackColor, 0.3f);
                _pressedColor = ControlPaint.Dark(BackColor, 0.2f);
            }
        }

        protected override void OnPaint(PaintEventArgs e)
        {
            Graphics g = e.Graphics;
            g.SmoothingMode = SmoothingMode.AntiAlias;
            g.TextRenderingHint = System.Drawing.Text.TextRenderingHint.ClearTypeGridFit;

            Rectangle rect = new Rectangle(0, 0, Width - 1, Height - 1);
            Color currentColor = BackColor;

            if (_isPressed)
                currentColor = _pressedColor;
            else if (_isHovered)
                currentColor = _hoverColor;

            // رسم الخلفية مع الحواف المدورة الأنيقة
            if (currentColor != Color.Transparent)
            {
                using (GraphicsPath path = GetRoundedRectangle(rect, _borderRadius))
                {
                    using (SolidBrush brush = new SolidBrush(currentColor))
                    {
                        g.FillPath(brush, path);
                    }

                    // إضافة حدود أنيقة عند التفاعل
                    if (_isHovered || _isPressed)
                    {
                        using (Pen borderPen = new Pen(ThemeManager.Instance.GetAccentColor(), 1))
                        {
                            g.DrawPath(borderPen, path);
                        }
                    }
                }
            }

            // رسم النص بجودة عالية
            using (SolidBrush textBrush = new SolidBrush(ForeColor))
            {
                StringFormat sf = new StringFormat
                {
                    Alignment = StringAlignment.Center,
                    LineAlignment = StringAlignment.Center
                };
                g.DrawString(Text, Font, textBrush, rect, sf);
            }
        }

        private GraphicsPath GetRoundedRectangle(Rectangle rect, int radius)
        {
            GraphicsPath path = new GraphicsPath();
            path.AddArc(rect.X, rect.Y, radius, radius, 180, 90);
            path.AddArc(rect.X + rect.Width - radius, rect.Y, radius, radius, 270, 90);
            path.AddArc(rect.X + rect.Width - radius, rect.Y + rect.Height - radius, radius, radius, 0, 90);
            path.AddArc(rect.X, rect.Y + rect.Height - radius, radius, radius, 90, 90);
            path.CloseAllFigures();
            return path;
        }

        protected override void OnMouseEnter(EventArgs e)
        {
            _isHovered = true;
            Invalidate();
            base.OnMouseEnter(e);
        }

        protected override void OnMouseLeave(EventArgs e)
        {
            _isHovered = false;
            Invalidate();
            base.OnMouseLeave(e);
        }

        protected override void OnMouseDown(MouseEventArgs e)
        {
            _isPressed = true;
            Invalidate();
            base.OnMouseDown(e);
        }

        protected override void OnMouseUp(MouseEventArgs e)
        {
            _isPressed = false;
            Invalidate();
            base.OnMouseUp(e);
        }
    }

    public class ModernTextBox : UserControl
    {
        private TextBox _textBox;
        private Label _placeholder;
        private Panel _borderPanel;
        private bool _isFocused = false;

        public ModernTextBox()
        {
            SetupControls();
            SetStyle(ControlStyles.AllPaintingInWmPaint |
                     ControlStyles.UserPaint |
                     ControlStyles.DoubleBuffer |
                     ControlStyles.ResizeRedraw, true);

            // تطبيق الألوان الافتراضية
            ApplyDefaultColors();
        }

        private void ApplyDefaultColors()
        {
            _textBox.BackColor = Color.White;
            _textBox.ForeColor = Color.Black;
            _borderPanel.BackColor = Color.White;
            base.BackColor = Color.White;
        }

        private void SetupControls()
        {
            Size = new Size(200, 40);

            _borderPanel = new Panel
            {
                Dock = DockStyle.Fill,
                BackColor = Color.White,
                Padding = new Padding(12, 8, 12, 8)
            };

            _textBox = new TextBox
            {
                BorderStyle = BorderStyle.None,
                Dock = DockStyle.Fill,
                Font = new Font("Segoe UI", 10),
                BackColor = Color.White,
                ForeColor = Color.Black
            };

            _placeholder = new Label
            {
                Text = "Enter text...",
                ForeColor = Color.Gray,
                Font = new Font("Segoe UI", 10),
                BackColor = Color.Transparent,
                AutoSize = false,
                Dock = DockStyle.Fill,
                TextAlign = ContentAlignment.MiddleLeft
            };

            _borderPanel.Controls.Add(_textBox);
            _borderPanel.Controls.Add(_placeholder);
            Controls.Add(_borderPanel);

            _textBox.TextChanged += TextBox_TextChanged;
            _textBox.Enter += TextBox_Enter;
            _textBox.Leave += TextBox_Leave;
            _placeholder.Click += (s, e) => _textBox.Focus();

            UpdatePlaceholderVisibility();
        }

        public string PlaceholderText
        {
            get { return _placeholder.Text; }
            set { _placeholder.Text = value; }
        }

        public override string Text
        {
            get { return _textBox.Text; }
            set { _textBox.Text = value; UpdatePlaceholderVisibility(); }
        }

        public new Font Font
        {
            get { return _textBox.Font; }
            set { _textBox.Font = value; _placeholder.Font = value; }
        }

        public new Color BackColor
        {
            get { return _textBox.BackColor; }
            set
            {
                _textBox.BackColor = value;
                _borderPanel.BackColor = value;
                base.BackColor = value;
                this.Invalidate();
            }
        }

        public new Color ForeColor
        {
            get { return _textBox.ForeColor; }
            set { _textBox.ForeColor = value; }
        }

        public Color BorderColor { get; set; } = Color.Gray;

        public bool UseSystemPasswordChar
        {
            get { return _textBox.UseSystemPasswordChar; }
            set { _textBox.UseSystemPasswordChar = value; }
        }

        private void TextBox_TextChanged(object sender, EventArgs e)
        {
            UpdatePlaceholderVisibility();
            OnTextChanged(e);
        }

        private void TextBox_Enter(object sender, EventArgs e)
        {
            _isFocused = true;
            UpdateBorderColor();
        }

        private void TextBox_Leave(object sender, EventArgs e)
        {
            _isFocused = false;
            UpdateBorderColor();
        }

        private void UpdatePlaceholderVisibility()
        {
            _placeholder.Visible = string.IsNullOrEmpty(_textBox.Text);
        }

        private void UpdateBorderColor()
        {
            Color borderColor = _isFocused ?
                ThemeManager.Instance.GetPrimaryColor() :
                BorderColor;

            // تطبيق لون الحدود (يمكن إضافة رسم الحدود هنا إذا لزم الأمر)
            Invalidate();
        }

        protected override void OnPaint(PaintEventArgs e)
        {
            Graphics g = e.Graphics;
            g.SmoothingMode = SmoothingMode.AntiAlias;

            Rectangle rect = new Rectangle(0, 0, Width - 1, Height - 1);
            Color borderColor = _isFocused ?
                ThemeManager.Instance.GetPrimaryColor() :
                BorderColor;

            using (GraphicsPath path = GetRoundedRectangle(rect, 6))
            {
                using (Pen borderPen = new Pen(borderColor, _isFocused ? 2 : 1))
                {
                    g.DrawPath(borderPen, path);
                }
            }
        }

        private GraphicsPath GetRoundedRectangle(Rectangle rect, int radius)
        {
            GraphicsPath path = new GraphicsPath();
            path.AddArc(rect.X, rect.Y, radius, radius, 180, 90);
            path.AddArc(rect.X + rect.Width - radius, rect.Y, radius, radius, 270, 90);
            path.AddArc(rect.X + rect.Width - radius, rect.Y + rect.Height - radius, radius, radius, 0, 90);
            path.AddArc(rect.X, rect.Y + rect.Height - radius, radius, radius, 90, 90);
            path.CloseAllFigures();
            return path;
        }
    }

    public class ModernPanel : Panel
    {
        private int _borderRadius = 10;
        private Color _shadowColor = Color.FromArgb(50, 0, 0, 0);
        private bool _hasShadow = true;

        public ModernPanel()
        {
            SetStyle(ControlStyles.AllPaintingInWmPaint | 
                     ControlStyles.UserPaint | 
                     ControlStyles.DoubleBuffer | 
                     ControlStyles.ResizeRedraw, true);
            
            BackColor = ThemeManager.Instance.GetSurfaceColor();
            Padding = new Padding(15);
        }

        public int BorderRadius
        {
            get { return _borderRadius; }
            set { _borderRadius = value; Invalidate(); }
        }

        public bool HasShadow
        {
            get { return _hasShadow; }
            set { _hasShadow = value; Invalidate(); }
        }

        protected override void OnPaint(PaintEventArgs e)
        {
            Graphics g = e.Graphics;
            g.SmoothingMode = SmoothingMode.AntiAlias;

            Rectangle rect = new Rectangle(0, 0, Width - 1, Height - 1);

            // رسم الظل
            if (_hasShadow)
            {
                Rectangle shadowRect = new Rectangle(rect.X + 3, rect.Y + 3, rect.Width, rect.Height);
                using (GraphicsPath shadowPath = GetRoundedRectangle(shadowRect, _borderRadius))
                {
                    using (SolidBrush shadowBrush = new SolidBrush(_shadowColor))
                    {
                        g.FillPath(shadowBrush, shadowPath);
                    }
                }
            }

            // رسم الخلفية
            using (GraphicsPath path = GetRoundedRectangle(rect, _borderRadius))
            {
                using (SolidBrush brush = new SolidBrush(BackColor))
                {
                    g.FillPath(brush, path);
                }

                // رسم الحدود
                using (Pen borderPen = new Pen(ThemeManager.Instance.GetBorderColor(), 1))
                {
                    g.DrawPath(borderPen, path);
                }
            }
        }

        private GraphicsPath GetRoundedRectangle(Rectangle rect, int radius)
        {
            GraphicsPath path = new GraphicsPath();

            // التحقق من صحة المعاملات
            if (radius <= 0 || rect.Width <= 0 || rect.Height <= 0)
            {
                path.AddRectangle(rect);
                return path;
            }

            // التأكد من أن نصف القطر لا يتجاوز نصف أبعاد المستطيل
            int maxRadius = Math.Min(rect.Width / 2, rect.Height / 2);
            if (radius > maxRadius)
                radius = maxRadius;

            // إذا كان نصف القطر صغير جداً، ارسم مستطيل عادي
            if (radius < 2)
            {
                path.AddRectangle(rect);
                return path;
            }

            try
            {
                path.AddArc(rect.X, rect.Y, radius * 2, radius * 2, 180, 90);
                path.AddArc(rect.X + rect.Width - radius * 2, rect.Y, radius * 2, radius * 2, 270, 90);
                path.AddArc(rect.X + rect.Width - radius * 2, rect.Y + rect.Height - radius * 2, radius * 2, radius * 2, 0, 90);
                path.AddArc(rect.X, rect.Y + rect.Height - radius * 2, radius * 2, radius * 2, 90, 90);
                path.CloseAllFigures();
            }
            catch
            {
                // في حالة حدوث خطأ، ارسم مستطيل عادي
                path.Reset();
                path.AddRectangle(rect);
            }

            return path;
        }
    }

    // بطاقة احترافية مع ظلال وتدرجات
    public class LegalCard : Panel
    {
        private int _borderRadius = 12;
        private bool _hasElevation = true;
        private int _elevation = 4;
        private Color _shadowColor = Color.FromArgb(50, 0, 0, 0);
        private bool _hasGradient = false;
        private Color _gradientStartColor = Color.White;
        private Color _gradientEndColor = Color.FromArgb(248, 250, 252);

        [Category("Legal Design")]
        public int BorderRadius
        {
            get => _borderRadius;
            set { _borderRadius = value; Invalidate(); }
        }

        [Category("Legal Design")]
        public bool HasElevation
        {
            get => _hasElevation;
            set { _hasElevation = value; Invalidate(); }
        }

        [Category("Legal Design")]
        public int Elevation
        {
            get => _elevation;
            set { _elevation = Math.Max(0, Math.Min(24, value)); Invalidate(); }
        }

        [Category("Legal Design")]
        public Color ShadowColor
        {
            get => _shadowColor;
            set { _shadowColor = value; Invalidate(); }
        }

        [Category("Legal Design")]
        public bool HasGradient
        {
            get => _hasGradient;
            set { _hasGradient = value; Invalidate(); }
        }

        [Category("Legal Design")]
        public Color GradientStartColor
        {
            get => _gradientStartColor;
            set { _gradientStartColor = value; Invalidate(); }
        }

        [Category("Legal Design")]
        public Color GradientEndColor
        {
            get => _gradientEndColor;
            set { _gradientEndColor = value; Invalidate(); }
        }

        public LegalCard()
        {
            SetStyle(ControlStyles.AllPaintingInWmPaint |
                     ControlStyles.UserPaint |
                     ControlStyles.DoubleBuffer |
                     ControlStyles.ResizeRedraw, true);

            BackColor = Color.White;
            Padding = new Padding(16);
        }

        protected override void OnPaint(PaintEventArgs e)
        {
            var graphics = e.Graphics;
            graphics.SmoothingMode = SmoothingMode.AntiAlias;

            var rect = new Rectangle(0, 0, Width, Height);

            // رسم الظل
            if (_hasElevation && _elevation > 0)
            {
                DrawElevation(graphics, rect);
            }

            // رسم الخلفية
            using (var path = GetRoundedRectangle(rect, _borderRadius))
            {
                if (_hasGradient)
                {
                    using (var gradientBrush = new LinearGradientBrush(
                        rect, _gradientStartColor, _gradientEndColor, LinearGradientMode.Vertical))
                    {
                        graphics.FillPath(gradientBrush, path);
                    }
                }
                else
                {
                    using (var brush = new SolidBrush(BackColor))
                    {
                        graphics.FillPath(brush, path);
                    }
                }

                // رسم الحدود
                using (var borderPen = new Pen(ThemeManager.Instance.GetBorderColor(), 1))
                {
                    graphics.DrawPath(borderPen, path);
                }
            }
        }

        private void DrawElevation(Graphics graphics, Rectangle rect)
        {
            for (int i = 0; i < _elevation; i++)
            {
                var shadowRect = new Rectangle(
                    rect.X + i,
                    rect.Y + i,
                    rect.Width,
                    rect.Height);

                var alpha = (int)(50 * (1.0 - (double)i / _elevation));
                var shadowColor = Color.FromArgb(alpha, _shadowColor.R, _shadowColor.G, _shadowColor.B);

                using (var shadowPath = GetRoundedRectangle(shadowRect, _borderRadius))
                using (var shadowBrush = new SolidBrush(shadowColor))
                {
                    graphics.FillPath(shadowBrush, shadowPath);
                }
            }
        }

        private GraphicsPath GetRoundedRectangle(Rectangle rect, int radius)
        {
            GraphicsPath path = new GraphicsPath();

            if (radius <= 0 || rect.Width <= 0 || rect.Height <= 0)
            {
                path.AddRectangle(rect);
                return path;
            }

            int maxRadius = Math.Min(rect.Width / 2, rect.Height / 2);
            if (radius > maxRadius)
                radius = maxRadius;

            if (radius < 2)
            {
                path.AddRectangle(rect);
                return path;
            }

            try
            {
                path.AddArc(rect.X, rect.Y, radius * 2, radius * 2, 180, 90);
                path.AddArc(rect.X + rect.Width - radius * 2, rect.Y, radius * 2, radius * 2, 270, 90);
                path.AddArc(rect.X + rect.Width - radius * 2, rect.Y + rect.Height - radius * 2, radius * 2, radius * 2, 0, 90);
                path.AddArc(rect.X, rect.Y + rect.Height - radius * 2, radius * 2, radius * 2, 90, 90);
                path.CloseAllFigures();
            }
            catch
            {
                path.Reset();
                path.AddRectangle(rect);
            }

            return path;
        }
    }

    // زر احترافي مع تأثيرات متقدمة
    public class LegalButton : Button
    {
        private int _borderRadius = 8;
        private bool _hasElevation = true;
        private int _elevation = 2;
        private bool _hasRippleEffect = true;
        private bool _isHovered = false;
        private bool _isPressed = false;
        private string _iconName = "";
        private int _iconSize = 20;

        [Category("Legal Design")]
        public int BorderRadius
        {
            get => _borderRadius;
            set { _borderRadius = value; Invalidate(); }
        }

        [Category("Legal Design")]
        public bool HasElevation
        {
            get => _hasElevation;
            set { _hasElevation = value; Invalidate(); }
        }

        [Category("Legal Design")]
        public int Elevation
        {
            get => _elevation;
            set { _elevation = Math.Max(0, Math.Min(8, value)); Invalidate(); }
        }

        [Category("Legal Design")]
        public bool HasRippleEffect
        {
            get => _hasRippleEffect;
            set { _hasRippleEffect = value; }
        }

        [Category("Legal Design")]
        public string IconName
        {
            get => _iconName;
            set { _iconName = value; UpdateIcon(); }
        }

        [Category("Legal Design")]
        public int IconSize
        {
            get => _iconSize;
            set { _iconSize = value; UpdateIcon(); }
        }

        private bool _hasGradient = false;
        private Color _gradientStartColor = Color.Empty;
        private Color _gradientEndColor = Color.Empty;

        [Category("Legal Design")]
        public bool HasGradient
        {
            get => _hasGradient;
            set { _hasGradient = value; Invalidate(); }
        }

        [Category("Legal Design")]
        public Color GradientStartColor
        {
            get => _gradientStartColor;
            set { _gradientStartColor = value; Invalidate(); }
        }

        [Category("Legal Design")]
        public Color GradientEndColor
        {
            get => _gradientEndColor;
            set { _gradientEndColor = value; Invalidate(); }
        }

        public LegalButton()
        {
            SetStyle(ControlStyles.AllPaintingInWmPaint |
                     ControlStyles.UserPaint |
                     ControlStyles.DoubleBuffer |
                     ControlStyles.ResizeRedraw, true);

            FlatStyle = FlatStyle.Flat;
            FlatAppearance.BorderSize = 0;
            Font = new Font("Segoe UI", 10, FontStyle.Bold);
            Cursor = Cursors.Hand;

            // إعداد الألوان الافتراضية
            BackColor = ThemeManager.Instance.GetPrimaryColor();
            ForeColor = ThemeManager.Instance.GetTextOnPrimaryColor();
        }

        protected override void OnMouseEnter(EventArgs e)
        {
            _isHovered = true;
            if (_hasElevation) _elevation = Math.Min(_elevation + 2, 8);
            Invalidate();
            base.OnMouseEnter(e);
        }

        protected override void OnMouseLeave(EventArgs e)
        {
            _isHovered = false;
            if (_hasElevation) _elevation = Math.Max(_elevation - 2, 2);
            Invalidate();
            base.OnMouseLeave(e);
        }

        protected override void OnMouseDown(MouseEventArgs mevent)
        {
            _isPressed = true;
            if (_hasElevation) _elevation = 1;
            Invalidate();
            base.OnMouseDown(mevent);
        }

        protected override void OnMouseUp(MouseEventArgs mevent)
        {
            _isPressed = false;
            if (_hasElevation) _elevation = _isHovered ? 4 : 2;
            Invalidate();
            base.OnMouseUp(mevent);
        }

        protected override void OnPaint(PaintEventArgs pevent)
        {
            var graphics = pevent.Graphics;
            graphics.SmoothingMode = SmoothingMode.AntiAlias;

            var rect = new Rectangle(0, 0, Width, Height);

            // رسم الظل
            if (_hasElevation && _elevation > 0)
            {
                DrawButtonElevation(graphics, rect);
            }

            // رسم الخلفية
            using (var path = GetRoundedRectangle(rect, _borderRadius))
            {
                if (_hasGradient && _gradientStartColor != Color.Empty && _gradientEndColor != Color.Empty)
                {
                    using (var brush = new LinearGradientBrush(rect, _gradientStartColor, _gradientEndColor, LinearGradientMode.Vertical))
                    {
                        graphics.FillPath(brush, path);
                    }
                }
                else
                {
                    var buttonColor = GetButtonColor();
                    using (var brush = new SolidBrush(buttonColor))
                    {
                        graphics.FillPath(brush, path);
                    }
                }
            }

            // رسم النص والأيقونة
            DrawButtonContent(graphics, rect);
        }

        private Color GetButtonColor()
        {
            var baseColor = BackColor;

            if (_isPressed)
            {
                return ControlPaint.Dark(baseColor, 0.2f);
            }
            else if (_isHovered)
            {
                return ControlPaint.Light(baseColor, 0.1f);
            }

            return baseColor;
        }

        private void DrawButtonElevation(Graphics graphics, Rectangle rect)
        {
            for (int i = 0; i < _elevation; i++)
            {
                var shadowRect = new Rectangle(
                    rect.X + i,
                    rect.Y + i + 1,
                    rect.Width,
                    rect.Height);

                var alpha = (int)(30 * (1.0 - (double)i / _elevation));
                var shadowColor = Color.FromArgb(alpha, 0, 0, 0);

                using (var shadowPath = GetRoundedRectangle(shadowRect, _borderRadius))
                using (var shadowBrush = new SolidBrush(shadowColor))
                {
                    graphics.FillPath(shadowBrush, shadowPath);
                }
            }
        }

        private void DrawButtonContent(Graphics graphics, Rectangle rect)
        {
            var contentRect = new Rectangle(
                rect.X + Padding.Left,
                rect.Y + Padding.Top,
                rect.Width - Padding.Horizontal,
                rect.Height - Padding.Vertical);

            // رسم الأيقونة والنص
            if (Image != null)
            {
                var imageRect = new Rectangle(
                    contentRect.X,
                    contentRect.Y + (contentRect.Height - Image.Height) / 2,
                    Image.Width,
                    Image.Height);

                graphics.DrawImage(Image, imageRect);

                // تعديل منطقة النص
                contentRect.X += Image.Width + 8;
                contentRect.Width -= Image.Width + 8;
            }

            // رسم النص
            if (!string.IsNullOrEmpty(Text))
            {
                using (var brush = new SolidBrush(ForeColor))
                {
                    var stringFormat = new StringFormat
                    {
                        Alignment = StringAlignment.Center,
                        LineAlignment = StringAlignment.Center
                    };

                    graphics.DrawString(Text, Font, brush, contentRect, stringFormat);
                }
            }
        }

        private void UpdateIcon()
        {
            if (!string.IsNullOrEmpty(_iconName))
            {
                Image = IconManager.CreateIcon(_iconName, _iconSize, ForeColor);
                ImageAlign = ContentAlignment.MiddleLeft;
                TextImageRelation = TextImageRelation.ImageBeforeText;
            }
        }

        private GraphicsPath GetRoundedRectangle(Rectangle rect, int radius)
        {
            GraphicsPath path = new GraphicsPath();

            if (radius <= 0 || rect.Width <= 0 || rect.Height <= 0)
            {
                path.AddRectangle(rect);
                return path;
            }

            int maxRadius = Math.Min(rect.Width / 2, rect.Height / 2);
            if (radius > maxRadius)
                radius = maxRadius;

            if (radius < 2)
            {
                path.AddRectangle(rect);
                return path;
            }

            try
            {
                path.AddArc(rect.X, rect.Y, radius * 2, radius * 2, 180, 90);
                path.AddArc(rect.X + rect.Width - radius * 2, rect.Y, radius * 2, radius * 2, 270, 90);
                path.AddArc(rect.X + rect.Width - radius * 2, rect.Y + rect.Height - radius * 2, radius * 2, radius * 2, 0, 90);
                path.AddArc(rect.X, rect.Y + rect.Height - radius * 2, radius * 2, radius * 2, 90, 90);
                path.CloseAllFigures();
            }
            catch
            {
                path.Reset();
                path.AddRectangle(rect);
            }

            return path;
        }
    }

    // شريط جانبي منزلق أنيق وسلس
    public class SlidingSidebar : Panel
    {
        private bool _isExpanded = true;
        private int _expandedWidth = 250;
        private int _collapsedWidth = 60;
        private System.Windows.Forms.Timer _animationTimer;
        private int _targetWidth;
        private int _animationSpeed = 15;
        private bool _isAnimating = false;

        // خصائص التصميم
        private Color _headerColor;
        private Color _itemHoverColor;
        private Color _itemSelectedColor;
        private Font _headerFont;
        private Font _itemFont;

        // العناصر
        private Panel _headerPanel;
        private Label _titleLabel;
        private ModernButton _toggleButton;
        private Panel _menuPanel;
        private List<SidebarItem> _menuItems;
        private SidebarItem _selectedItem;

        public SlidingSidebar()
        {
            InitializeComponent();
            SetupAnimation();
            ApplyTheme();
        }

        protected override void OnPaint(PaintEventArgs e)
        {
            base.OnPaint(e);

            Graphics g = e.Graphics;
            g.SmoothingMode = SmoothingMode.AntiAlias;

            // رسم ظل أنيق على الجانب الأيمن
            if (Width > _collapsedWidth + 10)
            {
                Rectangle shadowRect = new Rectangle(Width - 5, 0, 5, Height);
                using (var shadowBrush = new LinearGradientBrush(shadowRect,
                    Color.FromArgb(30, 0, 0, 0),
                    Color.Transparent,
                    LinearGradientMode.Horizontal))
                {
                    g.FillRectangle(shadowBrush, shadowRect);
                }
            }
        }

        private void InitializeComponent()
        {
            // إعدادات أساسية
            Size = new Size(_expandedWidth, 600);
            BackColor = ThemeManager.Instance.GetSecondaryColor();
            Dock = DockStyle.Left;

            // تحسين الأداء
            SetStyle(ControlStyles.AllPaintingInWmPaint |
                     ControlStyles.UserPaint |
                     ControlStyles.DoubleBuffer |
                     ControlStyles.ResizeRedraw, true);

            _menuItems = new List<SidebarItem>();

            CreateHeader();
            CreateMenuPanel();
        }

        private void CreateHeader()
        {
            _headerPanel = new Panel
            {
                Height = 80,
                Dock = DockStyle.Top,
                BackColor = ThemeManager.Instance.GetPrimaryColor(),
                Padding = new Padding(15, 10, 15, 10)
            };

            _titleLabel = new Label
            {
                Text = "سمارت لوير",
                Font = new Font("Segoe UI", 14, FontStyle.Bold),
                ForeColor = Color.White,
                AutoSize = false,
                Size = new Size(180, 30),
                Location = new Point(15, 25),
                TextAlign = ContentAlignment.MiddleLeft
            };

            _toggleButton = new ModernButton
            {
                Text = "☰",
                Size = new Size(40, 40),
                Location = new Point(200, 20),
                BackColor = Color.Transparent,
                ForeColor = Color.White,
                BorderRadius = 8,
                Font = new Font("Segoe UI", 16, FontStyle.Bold)
            };
            _toggleButton.Click += ToggleButton_Click;

            _headerPanel.Controls.Add(_titleLabel);
            _headerPanel.Controls.Add(_toggleButton);
            Controls.Add(_headerPanel);
        }

        private void CreateMenuPanel()
        {
            _menuPanel = new Panel
            {
                Dock = DockStyle.Fill,
                BackColor = Color.Transparent,
                AutoScroll = true,
                Padding = new Padding(0, 10, 0, 10)
            };
            Controls.Add(_menuPanel);
        }

        private void SetupAnimation()
        {
            _animationTimer = new System.Windows.Forms.Timer
            {
                Interval = 16 // ~60 FPS
            };
            _animationTimer.Tick += AnimationTimer_Tick;
        }

        private void ApplyTheme()
        {
            var theme = ThemeManager.Instance;
            _headerColor = theme.GetPrimaryColor();
            _itemHoverColor = Color.FromArgb(30, theme.GetAccentColor());
            _itemSelectedColor = theme.GetAccentColor();
            _headerFont = new Font("Segoe UI", 14, FontStyle.Bold);
            _itemFont = new Font("Segoe UI", 11, FontStyle.Regular);
        }

        // خصائص عامة
        public bool IsExpanded
        {
            get => _isExpanded;
            set
            {
                if (_isExpanded != value)
                {
                    _isExpanded = value;
                    AnimateToState();
                }
            }
        }

        public int ExpandedWidth
        {
            get => _expandedWidth;
            set { _expandedWidth = value; if (_isExpanded) Width = value; }
        }

        public int CollapsedWidth
        {
            get => _collapsedWidth;
            set { _collapsedWidth = value; if (!_isExpanded) Width = value; }
        }

        public int AnimationSpeed
        {
            get => _animationSpeed;
            set => _animationSpeed = Math.Max(1, Math.Min(50, value));
        }

        // إضافة عنصر قائمة
        public void AddMenuItem(string text, string iconName, EventHandler clickHandler)
        {
            var item = new SidebarItem(text, iconName, clickHandler, this);
            _menuItems.Add(item);
            _menuPanel.Controls.Add(item);
            UpdateMenuLayout();
        }

        // تحديث تخطيط القائمة
        private void UpdateMenuLayout()
        {
            int yPosition = 10;
            foreach (var item in _menuItems)
            {
                item.Location = new Point(0, yPosition);
                item.Width = _menuPanel.Width;
                yPosition += item.Height + 5;
            }
        }

        // تبديل حالة الشريط الجانبي
        private void ToggleButton_Click(object sender, EventArgs e)
        {
            Toggle();
        }

        public void Toggle()
        {
            IsExpanded = !IsExpanded;
        }

        public void Expand()
        {
            IsExpanded = true;
        }

        public void Collapse()
        {
            IsExpanded = false;
        }

        // الرسوم المتحركة
        private void AnimateToState()
        {
            if (_isAnimating) return;

            _targetWidth = _isExpanded ? _expandedWidth : _collapsedWidth;
            _isAnimating = true;
            _animationTimer.Start();
        }

        private void AnimationTimer_Tick(object sender, EventArgs e)
        {
            int currentWidth = Width;
            int difference = _targetWidth - currentWidth;

            // استخدام منحنى تسارع سلس (easing)
            if (Math.Abs(difference) <= 2)
            {
                Width = _targetWidth;
                _isAnimating = false;
                _animationTimer.Stop();
                OnAnimationComplete();
            }
            else
            {
                // تطبيق منحنى تسارع للحصول على حركة أكثر سلاسة
                int step = Math.Max(1, Math.Abs(difference) / 8);
                Width += Math.Sign(difference) * step;
            }

            UpdateUIForCurrentState();
            Invalidate(); // إعادة رسم للحصول على تأثيرات بصرية سلسة
        }

        private void OnAnimationComplete()
        {
            UpdateUIForCurrentState();
            foreach (var item in _menuItems)
            {
                item.UpdateForSidebarState(_isExpanded);
            }
        }

        private void UpdateUIForCurrentState()
        {
            // تحديث زر التبديل
            _toggleButton.Location = new Point(Width - 50, 20);

            // تحديث العنوان
            _titleLabel.Visible = Width > 100;

            // تحديث عناصر القائمة
            foreach (var item in _menuItems)
            {
                item.Width = Width;
                item.UpdateForSidebarState(Width > 100);
            }
        }

        protected override void OnResize(EventArgs e)
        {
            base.OnResize(e);
            UpdateMenuLayout();
        }

        // تحديد العنصر المحدد
        public void SetSelectedItem(SidebarItem item)
        {
            if (_selectedItem != null)
                _selectedItem.IsSelected = false;

            _selectedItem = item;
            if (_selectedItem != null)
                _selectedItem.IsSelected = true;
        }
    }

    // عنصر في الشريط الجانبي
    public class SidebarItem : Panel
    {
        private string _text;
        private string _iconName;
        private EventHandler _clickHandler;
        private SlidingSidebar _parentSidebar;
        private bool _isHovered = false;
        private bool _isSelected = false;

        private Label _iconLabel;
        private Label _textLabel;

        public SidebarItem(string text, string iconName, EventHandler clickHandler, SlidingSidebar parentSidebar)
        {
            _text = text;
            _iconName = iconName;
            _clickHandler = clickHandler;
            _parentSidebar = parentSidebar;

            InitializeComponent();
        }

        private void InitializeComponent()
        {
            Height = 50;
            BackColor = Color.Transparent;
            Cursor = Cursors.Hand;
            Padding = new Padding(15, 10, 15, 10);

            // تحسين الأداء
            SetStyle(ControlStyles.AllPaintingInWmPaint |
                     ControlStyles.UserPaint |
                     ControlStyles.DoubleBuffer |
                     ControlStyles.ResizeRedraw, true);

            CreateControls();
            AttachEvents();
        }

        private void CreateControls()
        {
            _iconLabel = new Label
            {
                Text = GetIconText(_iconName),
                Font = new Font("Segoe UI", 16, FontStyle.Regular),
                ForeColor = Color.White,
                Size = new Size(30, 30),
                Location = new Point(15, 10),
                TextAlign = ContentAlignment.MiddleCenter,
                BackColor = Color.Transparent
            };

            _textLabel = new Label
            {
                Text = _text,
                Font = new Font("Segoe UI", 11, FontStyle.Regular),
                ForeColor = Color.White,
                Size = new Size(150, 30),
                Location = new Point(55, 10),
                TextAlign = ContentAlignment.MiddleLeft,
                BackColor = Color.Transparent
            };

            Controls.Add(_iconLabel);
            Controls.Add(_textLabel);
        }

        private void AttachEvents()
        {
            Click += OnItemClick;
            _iconLabel.Click += OnItemClick;
            _textLabel.Click += OnItemClick;

            MouseEnter += OnMouseEnter;
            MouseLeave += OnMouseLeave;
            _iconLabel.MouseEnter += OnMouseEnter;
            _iconLabel.MouseLeave += OnMouseLeave;
            _textLabel.MouseEnter += OnMouseEnter;
            _textLabel.MouseLeave += OnMouseLeave;
        }

        private string GetIconText(string iconName)
        {
            return iconName switch
            {
                "clients" => "👥",
                "cases" => "📁",
                "hearings" => "⚖️",
                "appointments" => "📅",
                "procedures" => "📋",
                "expenses" => "💰",
                "users" => "👤",
                "notifications" => "🔔",
                "reports" => "📊",
                "settings" => "⚙️",
                "logout" => "🚪",
                _ => "📄"
            };
        }

        public bool IsSelected
        {
            get => _isSelected;
            set
            {
                _isSelected = value;
                Invalidate();
            }
        }

        public void UpdateForSidebarState(bool isExpanded)
        {
            _textLabel.Visible = isExpanded;
            if (isExpanded)
            {
                _iconLabel.Location = new Point(15, 10);
                _textLabel.Location = new Point(55, 10);
            }
            else
            {
                _iconLabel.Location = new Point((Width - 30) / 2, 10);
            }
        }

        private void OnItemClick(object sender, EventArgs e)
        {
            _parentSidebar.SetSelectedItem(this);
            _clickHandler?.Invoke(this, e);
        }

        private void OnMouseEnter(object sender, EventArgs e)
        {
            _isHovered = true;

            // تأثير تكبير خفيف للأيقونة
            if (_iconLabel != null)
            {
                _iconLabel.Font = new Font(_iconLabel.Font.FontFamily, _iconLabel.Font.Size + 2, _iconLabel.Font.Style);
            }

            Invalidate();
        }

        private void OnMouseLeave(object sender, EventArgs e)
        {
            _isHovered = false;

            // إعادة الأيقونة لحجمها الطبيعي
            if (_iconLabel != null)
            {
                _iconLabel.Font = new Font(_iconLabel.Font.FontFamily, 16, _iconLabel.Font.Style);
            }

            Invalidate();
        }

        protected override void OnPaint(PaintEventArgs e)
        {
            Graphics g = e.Graphics;
            g.SmoothingMode = SmoothingMode.AntiAlias;
            g.TextRenderingHint = System.Drawing.Text.TextRenderingHint.ClearTypeGridFit;

            Rectangle rect = new Rectangle(5, 0, Width - 10, Height - 2);

            // رسم الخلفية مع تدرج أنيق
            if (_isSelected)
            {
                using (var brush = new LinearGradientBrush(rect,
                    ThemeManager.Instance.GetAccentColor(),
                    ControlPaint.Light(ThemeManager.Instance.GetAccentColor(), 0.3f),
                    LinearGradientMode.Horizontal))
                using (var path = GetRoundedRectangle(rect, 8))
                {
                    g.FillPath(brush, path);
                }

                // إضافة حدود أنيقة للعنصر المحدد
                using (var pen = new Pen(ThemeManager.Instance.GetAccentColor(), 2))
                using (var path = GetRoundedRectangle(rect, 8))
                {
                    g.DrawPath(pen, path);
                }
            }
            else if (_isHovered)
            {
                using (var brush = new SolidBrush(Color.FromArgb(40, ThemeManager.Instance.GetAccentColor())))
                using (var path = GetRoundedRectangle(rect, 8))
                {
                    g.FillPath(brush, path);
                }
            }
        }

        private GraphicsPath GetRoundedRectangle(Rectangle rect, int radius)
        {
            GraphicsPath path = new GraphicsPath();
            path.AddArc(rect.X, rect.Y, radius, radius, 180, 90);
            path.AddArc(rect.X + rect.Width - radius, rect.Y, radius, radius, 270, 90);
            path.AddArc(rect.X + rect.Width - radius, rect.Y + rect.Height - radius, radius, radius, 0, 90);
            path.AddArc(rect.X, rect.Y + rect.Height - radius, radius, radius, 90, 90);
            path.CloseAllFigures();
            return path;
        }
    }
}
