<?xml version="1.0" encoding="utf-8"?>
<configuration>
    <appSettings>
        <!-- إعدادات التطبيق العامة -->
        <add key="ApplicationName" value="Smart Lawyer" />
        <add key="ApplicationVersion" value="1.0.0" />
        <add key="Developer" value="Generation Five" />
        <add key="DeveloperPhone" value="+212 661 77 53 72" />
        <add key="DeveloperWebsite" value="www.generationfive.net" />
        
        <!-- إعدادات قاعدة البيانات -->
        <add key="DatabasePath" value="SmartLawyer.db" />
        <add key="ConnectionTimeout" value="30" />
        <add key="CommandTimeout" value="60" />
        
        <!-- إعدادات اللغة -->
        <add key="DefaultLanguage" value="ar" />
        <add key="SupportedLanguages" value="ar,fr" />
        
        <!-- إعدادات النسخ الاحتياطي -->
        <add key="AutoBackup" value="true" />
        <add key="BackupInterval" value="24" />
        <add key="BackupPath" value="Backups" />
        <add key="MaxBackupFiles" value="10" />
        
        <!-- إعدادات الإشعارات -->
        <add key="EmailNotifications" value="true" />
        <add key="WhatsAppNotifications" value="false" />
        <add key="ReminderMinutes" value="30" />
        
        <!-- إعدادات الأمان -->
        <add key="SessionTimeout" value="480" />
        <add key="MaxLoginAttempts" value="3" />
        <add key="PasswordMinLength" value="6" />
        
        <!-- إعدادات التقارير -->
        <add key="DefaultReportFormat" value="PDF" />
        <add key="ReportsPath" value="Reports" />
        <add key="TempPath" value="Temp" />
        
        <!-- إعدادات الواجهة -->
        <add key="Theme" value="Default" />
        <add key="FontSize" value="10" />
        <add key="WindowState" value="Maximized" />
    </appSettings>
    
    <connectionStrings>
        <add name="DefaultConnection" 
             connectionString="Data Source=SmartLawyer.db;Version=3;Pooling=true;Max Pool Size=100;" 
             providerName="System.Data.SQLite" />
    </connectionStrings>
    
    <system.diagnostics>
        <trace autoflush="true">
            <listeners>
                <add name="fileListener" 
                     type="System.Diagnostics.TextWriterTraceListener" 
                     initializeData="Logs\SmartLawyer.log" />
            </listeners>
        </trace>
    </system.diagnostics>
    
    <runtime>
        <assemblyBinding xmlns="urn:schemas-microsoft-com:asm.v1">
            <dependentAssembly>
                <assemblyIdentity name="System.Data.SQLite" 
                                  publicKeyToken="db937bc2d44ff139" 
                                  culture="neutral" />
                <bindingRedirect oldVersion="0.0.0.0-1.0.119.0" 
                                 newVersion="1.0.119.0" />
            </dependentAssembly>
        </assemblyBinding>
    </runtime>
</configuration>
