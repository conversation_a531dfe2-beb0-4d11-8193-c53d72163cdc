using System;

namespace SmartLawyer.Models
{
    public class Client
    {
        public int Id { get; set; }
        public string ClientType { get; set; } // Individual, Company, Administration
        public string FirstName { get; set; }
        public string LastName { get; set; }
        public string CompanyName { get; set; }
        public string CIN { get; set; }
        public string ICE { get; set; }
        public string Address { get; set; }
        public string City { get; set; }
        public string Phone { get; set; }
        public string Email { get; set; }
        public string Notes { get; set; }
        public DateTime CreatedDate { get; set; }
        public int CreatedBy { get; set; }

        public string FullName
        {
            get
            {
                if (ClientType == "Company" || ClientType == "Administration")
                    return CompanyName;
                else
                    return $"{FirstName} {LastName}";
            }
        }

        public string DisplayText
        {
            get
            {
                return $"{FullName} - {ClientType}";
            }
        }
    }

    public class Case
    {
        public int Id { get; set; }
        public string CaseNumber { get; set; }
        public string Title { get; set; }
        public string CaseType { get; set; } // Civil, Commercial, Criminal, Administrative
        public int ClientId { get; set; }
        public string Court { get; set; }
        public string Judge { get; set; }
        public string Status { get; set; } // Open, Closed, Suspended
        public string Description { get; set; }
        public DateTime? StartDate { get; set; }
        public DateTime? EndDate { get; set; }
        public DateTime CreatedDate { get; set; }
        public int CreatedBy { get; set; }

        // Navigation properties
        public Client Client { get; set; }
    }

    public class Hearing
    {
        public int Id { get; set; }
        public int CaseId { get; set; }
        public DateTime HearingDate { get; set; }
        public string Court { get; set; }
        public string Room { get; set; }
        public string Judge { get; set; }
        public string HearingType { get; set; } // Initial, Follow-up, Final
        public string Status { get; set; } // Scheduled, Completed, Postponed, Cancelled
        public string Notes { get; set; }
        public bool Reminder { get; set; }
        public DateTime CreatedDate { get; set; }
        public int CreatedBy { get; set; }

        // Navigation properties
        public Case Case { get; set; }
    }

    public class Appointment
    {
        public int Id { get; set; }
        public int? ClientId { get; set; }
        public string Title { get; set; }
        public string Description { get; set; }
        public DateTime AppointmentDate { get; set; }
        public string Location { get; set; }
        public string Status { get; set; } // Scheduled, Completed, Cancelled
        public bool Reminder { get; set; }
        public DateTime CreatedDate { get; set; }
        public int CreatedBy { get; set; }

        // Navigation properties
        public Client Client { get; set; }
    }

    public class Procedure
    {
        public int Id { get; set; }
        public int CaseId { get; set; }
        public string ProcedureType { get; set; } // Summons, Notification, Expertise, Appeal
        public string Description { get; set; }
        public DateTime ProcedureDate { get; set; }
        public string Location { get; set; }
        public string Status { get; set; } // Pending, Completed, Failed
        public decimal Cost { get; set; }
        public string Notes { get; set; }
        public DateTime CreatedDate { get; set; }
        public int CreatedBy { get; set; }

        // Navigation properties
        public Case Case { get; set; }
    }

    public class Expense
    {
        public int Id { get; set; }
        public int? CaseId { get; set; }
        public string ExpenseType { get; set; } // Court Fees, Printing, Transport, Other
        public string Description { get; set; }
        public decimal Amount { get; set; }
        public DateTime ExpenseDate { get; set; }
        public string Receipt { get; set; } // Path to receipt file
        public string Notes { get; set; }
        public DateTime CreatedDate { get; set; }
        public int CreatedBy { get; set; }

        // Navigation properties
        public Case Case { get; set; }
    }

    public class Document
    {
        public int Id { get; set; }
        public int? CaseId { get; set; }
        public int? ClientId { get; set; }
        public string DocumentName { get; set; }
        public string DocumentType { get; set; } // PDF, Word, Image, Other
        public string FilePath { get; set; }
        public long FileSize { get; set; }
        public string Description { get; set; }
        public DateTime UploadDate { get; set; }
        public int UploadedBy { get; set; }

        // Navigation properties
        public Case Case { get; set; }
        public Client Client { get; set; }
    }

    public class User
    {
        public int Id { get; set; }
        public string Username { get; set; }
        public string Password { get; set; }
        public string FullName { get; set; }
        public string Email { get; set; }
        public string Phone { get; set; }
        public string Role { get; set; } // Admin, Lawyer, Secretary, User
        public bool IsActive { get; set; }
        public DateTime CreatedDate { get; set; }
        public DateTime? LastLogin { get; set; }
    }

    public class ActivityLog
    {
        public int Id { get; set; }
        public int UserId { get; set; }
        public string Action { get; set; }
        public string TableName { get; set; }
        public int? RecordId { get; set; }
        public string OldValues { get; set; }
        public string NewValues { get; set; }
        public DateTime Timestamp { get; set; }

        // Navigation properties
        public User User { get; set; }
    }

    public class Setting
    {
        public int Id { get; set; }
        public string SettingKey { get; set; }
        public string SettingValue { get; set; }
        public string Description { get; set; }
        public DateTime UpdatedDate { get; set; }
        public int? UpdatedBy { get; set; }

        // Navigation properties
        public User UpdatedByUser { get; set; }
    }
}
