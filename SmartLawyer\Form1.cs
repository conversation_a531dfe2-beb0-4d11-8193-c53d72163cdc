using SmartLawyer.Forms;
using SmartLawyer.UI;
using SmartLawyer.Localization;

namespace SmartLawyer;

public partial class Form1 : Form
{
    public Form1()
    {
        InitializeComponent();
    }

    private void Form1_Load(object sender, EventArgs e)
    {
        // إخفاء النافذة الرئيسية وعرض نافذة تسجيل الدخول
        this.Hide();
        LoginForm loginForm = new LoginForm();
        if (loginForm.ShowDialog() == DialogResult.OK)
        {
            this.Show();
            SetupEventHandlers();
            UpdateLanguage();
            ApplyTheme();
            ShowDashboard();

            // تطبيق إعدادات اللغة
            LanguageManager.Instance.ApplyLanguageToForm(this);

            // تطبيق انتقالات الظهور
            AnimationManager.AnimateFormLoad(this);
            AnimateMenuButtons();
        }
        else
        {
            Application.Exit();
        }
    }

    private void SetupEventHandlers()
    {
        btnClients.Click += BtnClients_Click;
        btnCases.Click += BtnCases_Click;
        btnHearings.Click += BtnHearings_Click;
        btnAppointments.Click += BtnAppointments_Click;
        btnProcedures.Click += BtnProcedures_Click;
        btnExpenses.Click += BtnExpenses_Click;
        btnUsers.Click += BtnUsers_Click;
        btnNotifications.Click += BtnNotifications_Click;
        btnReports.Click += BtnReports_Click;
        btnSettings.Click += BtnSettings_Click;
        btnLogout.Click += BtnLogout_Click;
    }

    private void ShowDashboard()
    {
        mainPanel.Controls.Clear();

        Label lblDashboard = new Label
        {
            Text = "مرحباً بك في سمارت لوير\nبرنامج إدارة مكتب المحاماة",
            Font = new Font("Segoe UI", 24, FontStyle.Bold),
            ForeColor = Color.FromArgb(52, 73, 94),
            TextAlign = ContentAlignment.MiddleCenter,
            Dock = DockStyle.Fill
        };

        mainPanel.Controls.Add(lblDashboard);
    }

    private void BtnClients_Click(object sender, EventArgs e)
    {
        ShowForm(new ClientsForm());
    }

    private void BtnCases_Click(object sender, EventArgs e)
    {
        MessageBox.Show("وحدة الملفات والقضايا قيد التطوير", "معلومات", MessageBoxButtons.OK, MessageBoxIcon.Information);
    }

    private void BtnHearings_Click(object sender, EventArgs e)
    {
        MessageBox.Show("وحدة الجلسات قيد التطوير", "معلومات", MessageBoxButtons.OK, MessageBoxIcon.Information);
    }

    private void BtnAppointments_Click(object sender, EventArgs e)
    {
        MessageBox.Show("وحدة المواعيد قيد التطوير", "معلومات", MessageBoxButtons.OK, MessageBoxIcon.Information);
    }

    private void BtnProcedures_Click(object sender, EventArgs e)
    {
        MessageBox.Show("وحدة الإجراءات قيد التطوير", "معلومات", MessageBoxButtons.OK, MessageBoxIcon.Information);
    }

    private void BtnExpenses_Click(object sender, EventArgs e)
    {
        MessageBox.Show("وحدة المصاريف قيد التطوير", "معلومات", MessageBoxButtons.OK, MessageBoxIcon.Information);
    }

    private void BtnUsers_Click(object sender, EventArgs e)
    {
        MessageBox.Show("وحدة المستخدمين قيد التطوير", "معلومات", MessageBoxButtons.OK, MessageBoxIcon.Information);
    }

    private void BtnNotifications_Click(object sender, EventArgs e)
    {
        MessageBox.Show("وحدة الإشعارات قيد التطوير", "معلومات", MessageBoxButtons.OK, MessageBoxIcon.Information);
    }

    private void BtnReports_Click(object sender, EventArgs e)
    {
        MessageBox.Show("وحدة التقارير قيد التطوير", "معلومات", MessageBoxButtons.OK, MessageBoxIcon.Information);
    }

    private void BtnSettings_Click(object sender, EventArgs e)
    {
        MessageBox.Show("وحدة الإعدادات قيد التطوير", "معلومات", MessageBoxButtons.OK, MessageBoxIcon.Information);
    }

    private void BtnLogout_Click(object sender, EventArgs e)
    {
        var result = MessageBox.Show("هل تريد تسجيل الخروج؟", "تأكيد", MessageBoxButtons.YesNo, MessageBoxIcon.Question);
        if (result == DialogResult.Yes)
        {
            Application.Restart();
        }
    }

    private void ShowForm(Form form)
    {
        // تأثير انتقال سلس
        AnimationManager.FadeIn(mainPanel, 300);

        mainPanel.Controls.Clear();
        form.TopLevel = false;
        form.FormBorderStyle = FormBorderStyle.None;
        form.Dock = DockStyle.Fill;
        mainPanel.Controls.Add(form);
        form.Show();

        // تطبيق إعدادات اللغة على النافذة الجديدة
        LanguageManager.Instance.ApplyLanguageToForm(form);

        // تأثير ظهور النافذة
        AnimationManager.SlideIn(form, SlideDirection.FromRight, 400);
    }

    private void ApplyTheme()
    {
        var theme = ThemeManager.Instance;

        // تطبيق ألوان الثيم
        this.BackColor = theme.GetBackgroundColor();
        topPanel.BackColor = theme.GetPrimaryColor();
        sidePanel.BackColor = Color.FromArgb(44, 62, 80);
        mainPanel.BackColor = theme.GetSurfaceColor();

        // تحديث ألوان النصوص
        lblWelcome.ForeColor = Color.White;
        lblDateTime.ForeColor = Color.White;

        // تطبيق الخطوط
        var currentLang = LanguageManager.Instance.CurrentLanguageInfo;
        Font newFont = new Font(currentLang.FontFamily, 12, FontStyle.Bold);

        foreach (Control control in sidePanel.Controls)
        {
            if (control is ModernButton button)
            {
                button.Font = newFont;
            }
        }
    }

    private void AnimateMenuButtons()
    {
        // تأثير ظهور تدريجي للأزرار
        var buttons = new[] { btnClients, btnCases, btnHearings, btnAppointments,
                             btnProcedures, btnExpenses, btnUsers, btnNotifications,
                             btnReports, btnSettings, btnLogout };

        for (int i = 0; i < buttons.Length; i++)
        {
            System.Windows.Forms.Timer delayTimer = new System.Windows.Forms.Timer();
            delayTimer.Interval = (i + 1) * 100; // تأخير متدرج (بدءاً من 100ms)
            var button = buttons[i];

            delayTimer.Tick += (s, e) =>
            {
                AnimationManager.SlideIn(button, SlideDirection.FromLeft, 300);
                ((System.Windows.Forms.Timer)s).Stop();
                ((System.Windows.Forms.Timer)s).Dispose();
            };
            delayTimer.Start();
        }
    }
}
