using System;
using System.Data.SQLite;
using System.Drawing;
using System.Windows.Forms;
using SmartLawyer.Database;
using SmartLawyer.Localization;
using SmartLawyer.Models;

namespace SmartLawyer.Forms
{
    public partial class ClientEditForm : Form
    {
        private int _clientId;
        private bool _isEditMode;

        private ComboBox cmbClientType;
        private TextBox txtFirstName;
        private TextBox txtLastName;
        private TextBox txtCompanyName;
        private TextBox txtCIN;
        private TextBox txtICE;
        private TextBox txtAddress;
        private TextBox txtCity;
        private TextBox txtPhone;
        private TextBox txtEmail;
        private TextBox txtNotes;
        private Button btnSave;
        private Button btnCancel;

        private Label lblClientType;
        private Label lblFirstName;
        private Label lblLastName;
        private Label lblCompanyName;
        private Label lblCIN;
        private Label lblICE;
        private Label lblAddress;
        private Label lblCity;
        private Label lblPhone;
        private Label lblEmail;
        private Label lblNotes;

        public ClientEditForm(int clientId = 0)
        {
            _clientId = clientId;
            _isEditMode = clientId > 0;
            InitializeComponent();
            LoadLanguage();
            
            if (_isEditMode)
            {
                LoadClientData();
            }
            else
            {
                cmbClientType.SelectedIndex = 0;
                UpdateFieldsVisibility();
            }
        }

        private void InitializeComponent()
        {
            this.SuspendLayout();

            // إعداد النافذة
            this.Text = _isEditMode ? "تعديل بيانات الموكل" : "إضافة موكل جديد";
            this.Size = new Size(600, 700);
            this.StartPosition = FormStartPosition.CenterParent;
            this.FormBorderStyle = FormBorderStyle.FixedDialog;
            this.MaximizeBox = false;
            this.MinimizeBox = false;
            this.BackColor = Color.FromArgb(240, 240, 240);

            int y = 20;
            int labelWidth = 120;
            int textBoxWidth = 400;
            int spacing = 40;

            // نوع الموكل
            lblClientType = CreateLabel("نوع الموكل:", y);
            cmbClientType = new ComboBox
            {
                Location = new Point(150, y),
                Size = new Size(textBoxWidth, 30),
                Font = new Font("Segoe UI", 10),
                DropDownStyle = ComboBoxStyle.DropDownList
            };
            cmbClientType.Items.Add("فرد");
            cmbClientType.Items.Add("شركة");
            cmbClientType.Items.Add("إدارة");
            cmbClientType.SelectedIndexChanged += CmbClientType_SelectedIndexChanged;
            y += spacing;

            // الاسم الأول
            lblFirstName = CreateLabel("الاسم الأول:", y);
            txtFirstName = CreateTextBox(y);
            y += spacing;

            // الاسم الأخير
            lblLastName = CreateLabel("الاسم الأخير:", y);
            txtLastName = CreateTextBox(y);
            y += spacing;

            // اسم الشركة
            lblCompanyName = CreateLabel("اسم الشركة:", y);
            txtCompanyName = CreateTextBox(y);
            y += spacing;

            // رقم البطاقة الوطنية
            lblCIN = CreateLabel("رقم البطاقة الوطنية:", y);
            txtCIN = CreateTextBox(y);
            y += spacing;

            // رقم السجل التجاري
            lblICE = CreateLabel("رقم السجل التجاري:", y);
            txtICE = CreateTextBox(y);
            y += spacing;

            // العنوان
            lblAddress = CreateLabel("العنوان:", y);
            txtAddress = CreateTextBox(y);
            y += spacing;

            // المدينة
            lblCity = CreateLabel("المدينة:", y);
            txtCity = CreateTextBox(y);
            y += spacing;

            // الهاتف
            lblPhone = CreateLabel("الهاتف:", y);
            txtPhone = CreateTextBox(y);
            y += spacing;

            // البريد الإلكتروني
            lblEmail = CreateLabel("البريد الإلكتروني:", y);
            txtEmail = CreateTextBox(y);
            y += spacing;

            // الملاحظات
            lblNotes = CreateLabel("ملاحظات:", y);
            txtNotes = new TextBox
            {
                Location = new Point(150, y),
                Size = new Size(textBoxWidth, 80),
                Font = new Font("Segoe UI", 10),
                RightToLeft = RightToLeft.Yes,
                Multiline = true,
                ScrollBars = ScrollBars.Vertical
            };
            y += 100;

            // أزرار الحفظ والإلغاء
            btnSave = new Button
            {
                Text = "حفظ",
                Location = new Point(150, y),
                Size = new Size(120, 40),
                Font = new Font("Segoe UI", 12, FontStyle.Bold),
                BackColor = Color.FromArgb(46, 204, 113),
                ForeColor = Color.White,
                FlatStyle = FlatStyle.Flat,
                Cursor = Cursors.Hand
            };
            btnSave.FlatAppearance.BorderSize = 0;
            btnSave.Click += BtnSave_Click;

            btnCancel = new Button
            {
                Text = "إلغاء",
                Location = new Point(280, y),
                Size = new Size(120, 40),
                Font = new Font("Segoe UI", 12, FontStyle.Bold),
                BackColor = Color.FromArgb(231, 76, 60),
                ForeColor = Color.White,
                FlatStyle = FlatStyle.Flat,
                Cursor = Cursors.Hand
            };
            btnCancel.FlatAppearance.BorderSize = 0;
            btnCancel.Click += BtnCancel_Click;

            // إضافة العناصر إلى النافذة
            this.Controls.Add(lblClientType);
            this.Controls.Add(cmbClientType);
            this.Controls.Add(lblFirstName);
            this.Controls.Add(txtFirstName);
            this.Controls.Add(lblLastName);
            this.Controls.Add(txtLastName);
            this.Controls.Add(lblCompanyName);
            this.Controls.Add(txtCompanyName);
            this.Controls.Add(lblCIN);
            this.Controls.Add(txtCIN);
            this.Controls.Add(lblICE);
            this.Controls.Add(txtICE);
            this.Controls.Add(lblAddress);
            this.Controls.Add(txtAddress);
            this.Controls.Add(lblCity);
            this.Controls.Add(txtCity);
            this.Controls.Add(lblPhone);
            this.Controls.Add(txtPhone);
            this.Controls.Add(lblEmail);
            this.Controls.Add(txtEmail);
            this.Controls.Add(lblNotes);
            this.Controls.Add(txtNotes);
            this.Controls.Add(btnSave);
            this.Controls.Add(btnCancel);

            this.ResumeLayout(false);
        }

        private Label CreateLabel(string text, int y)
        {
            return new Label
            {
                Text = text,
                Location = new Point(20, y + 5),
                Size = new Size(120, 25),
                Font = new Font("Segoe UI", 10, FontStyle.Bold),
                TextAlign = ContentAlignment.MiddleRight
            };
        }

        private TextBox CreateTextBox(int y)
        {
            return new TextBox
            {
                Location = new Point(150, y),
                Size = new Size(400, 30),
                Font = new Font("Segoe UI", 10),
                RightToLeft = RightToLeft.Yes
            };
        }

        private void LoadLanguage()
        {
            var lang = LanguageManager.Instance;
            
            this.Text = _isEditMode ? lang.GetText("EditClient") : lang.GetText("AddClient");
            lblClientType.Text = lang.GetText("ClientType") + ":";
            lblFirstName.Text = lang.GetText("FirstName") + ":";
            lblLastName.Text = lang.GetText("LastName") + ":";
            lblCompanyName.Text = lang.GetText("CompanyName") + ":";
            lblCIN.Text = lang.GetText("CIN") + ":";
            lblICE.Text = lang.GetText("ICE") + ":";
            lblAddress.Text = lang.GetText("Address") + ":";
            lblCity.Text = lang.GetText("City") + ":";
            lblPhone.Text = lang.GetText("Phone") + ":";
            lblEmail.Text = lang.GetText("Email") + ":";
            lblNotes.Text = lang.GetText("Notes") + ":";
            btnSave.Text = lang.GetText("Save");
            btnCancel.Text = lang.GetText("Cancel");

            // تحديث عناصر القائمة المنسدلة
            cmbClientType.Items.Clear();
            cmbClientType.Items.Add(lang.GetText("Individual"));
            cmbClientType.Items.Add(lang.GetText("Company"));
            cmbClientType.Items.Add(lang.GetText("Administration"));
        }

        private void CmbClientType_SelectedIndexChanged(object sender, EventArgs e)
        {
            UpdateFieldsVisibility();
        }

        private void UpdateFieldsVisibility()
        {
            bool isIndividual = cmbClientType.SelectedIndex == 0;
            bool isCompany = cmbClientType.SelectedIndex == 1;

            // إظهار/إخفاء الحقول حسب نوع الموكل
            lblFirstName.Visible = isIndividual;
            txtFirstName.Visible = isIndividual;
            lblLastName.Visible = isIndividual;
            txtLastName.Visible = isIndividual;
            lblCIN.Visible = isIndividual;
            txtCIN.Visible = isIndividual;

            lblCompanyName.Visible = !isIndividual;
            txtCompanyName.Visible = !isIndividual;
            lblICE.Visible = isCompany;
            txtICE.Visible = isCompany;
        }

        private void LoadClientData()
        {
            try
            {
                using (var connection = DatabaseManager.Instance.GetConnection())
                {
                    connection.Open();
                    string query = "SELECT * FROM Clients WHERE Id = @id";
                    using (var command = new SQLiteCommand(query, connection))
                    {
                        command.Parameters.AddWithValue("@id", _clientId);
                        using (var reader = command.ExecuteReader())
                        {
                            if (reader.Read())
                            {
                                string clientType = reader["ClientType"].ToString();
                                switch (clientType)
                                {
                                    case "Individual":
                                        cmbClientType.SelectedIndex = 0;
                                        break;
                                    case "Company":
                                        cmbClientType.SelectedIndex = 1;
                                        break;
                                    case "Administration":
                                        cmbClientType.SelectedIndex = 2;
                                        break;
                                }

                                txtFirstName.Text = reader["FirstName"].ToString();
                                txtLastName.Text = reader["LastName"].ToString();
                                txtCompanyName.Text = reader["CompanyName"].ToString();
                                txtCIN.Text = reader["CIN"].ToString();
                                txtICE.Text = reader["ICE"].ToString();
                                txtAddress.Text = reader["Address"].ToString();
                                txtCity.Text = reader["City"].ToString();
                                txtPhone.Text = reader["Phone"].ToString();
                                txtEmail.Text = reader["Email"].ToString();
                                txtNotes.Text = reader["Notes"].ToString();

                                UpdateFieldsVisibility();
                            }
                        }
                    }
                }
            }
            catch (Exception ex)
            {
                MessageBox.Show($"خطأ في تحميل بيانات الموكل: {ex.Message}", "خطأ", 
                               MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        private void BtnSave_Click(object sender, EventArgs e)
        {
            if (ValidateInput())
            {
                SaveClient();
            }
        }

        private bool ValidateInput()
        {
            var lang = LanguageManager.Instance;
            
            if (cmbClientType.SelectedIndex == 0) // فرد
            {
                if (string.IsNullOrWhiteSpace(txtFirstName.Text) || string.IsNullOrWhiteSpace(txtLastName.Text))
                {
                    MessageBox.Show("يجب إدخال الاسم الأول والأخير للأفراد", lang.GetText("ValidationError"), 
                                   MessageBoxButtons.OK, MessageBoxIcon.Warning);
                    return false;
                }
            }
            else // شركة أو إدارة
            {
                if (string.IsNullOrWhiteSpace(txtCompanyName.Text))
                {
                    MessageBox.Show("يجب إدخال اسم الشركة أو الإدارة", lang.GetText("ValidationError"), 
                                   MessageBoxButtons.OK, MessageBoxIcon.Warning);
                    return false;
                }
            }

            return true;
        }

        private void SaveClient()
        {
            try
            {
                using (var connection = DatabaseManager.Instance.GetConnection())
                {
                    connection.Open();
                    
                    string query;
                    if (_isEditMode)
                    {
                        query = @"UPDATE Clients SET 
                                    ClientType = @clientType, FirstName = @firstName, LastName = @lastName,
                                    CompanyName = @companyName, CIN = @cin, ICE = @ice, Address = @address,
                                    City = @city, Phone = @phone, Email = @email, Notes = @notes
                                  WHERE Id = @id";
                    }
                    else
                    {
                        query = @"INSERT INTO Clients 
                                    (ClientType, FirstName, LastName, CompanyName, CIN, ICE, Address, 
                                     City, Phone, Email, Notes, CreatedDate, CreatedBy)
                                  VALUES 
                                    (@clientType, @firstName, @lastName, @companyName, @cin, @ice, @address,
                                     @city, @phone, @email, @notes, @createdDate, @createdBy)";
                    }

                    using (var command = new SQLiteCommand(query, connection))
                    {
                        string clientType = "";
                        switch (cmbClientType.SelectedIndex)
                        {
                            case 0: clientType = "Individual"; break;
                            case 1: clientType = "Company"; break;
                            case 2: clientType = "Administration"; break;
                        }

                        command.Parameters.AddWithValue("@clientType", clientType);
                        command.Parameters.AddWithValue("@firstName", txtFirstName.Text);
                        command.Parameters.AddWithValue("@lastName", txtLastName.Text);
                        command.Parameters.AddWithValue("@companyName", txtCompanyName.Text);
                        command.Parameters.AddWithValue("@cin", txtCIN.Text);
                        command.Parameters.AddWithValue("@ice", txtICE.Text);
                        command.Parameters.AddWithValue("@address", txtAddress.Text);
                        command.Parameters.AddWithValue("@city", txtCity.Text);
                        command.Parameters.AddWithValue("@phone", txtPhone.Text);
                        command.Parameters.AddWithValue("@email", txtEmail.Text);
                        command.Parameters.AddWithValue("@notes", txtNotes.Text);

                        if (_isEditMode)
                        {
                            command.Parameters.AddWithValue("@id", _clientId);
                        }
                        else
                        {
                            command.Parameters.AddWithValue("@createdDate", DateTime.Now);
                            command.Parameters.AddWithValue("@createdBy", 1); // TODO: استخدام معرف المستخدم الحالي
                        }

                        command.ExecuteNonQuery();
                    }

                    MessageBox.Show(LanguageManager.Instance.GetText("SaveSuccess"), "نجح", 
                                   MessageBoxButtons.OK, MessageBoxIcon.Information);
                    this.DialogResult = DialogResult.OK;
                    this.Close();
                }
            }
            catch (Exception ex)
            {
                MessageBox.Show($"خطأ في حفظ بيانات الموكل: {ex.Message}", "خطأ", 
                               MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        private void BtnCancel_Click(object sender, EventArgs e)
        {
            this.DialogResult = DialogResult.Cancel;
            this.Close();
        }
    }
}
