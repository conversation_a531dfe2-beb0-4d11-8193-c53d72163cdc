using System;
using System.Collections.Generic;
using System.Drawing;
using System.Drawing.Drawing2D;
using System.Windows.Forms;

namespace SmartLawyer.UI
{
    // شريط جانبي منزلق بسيط وأنيق
    public class SimpleSlidingSidebar : Panel
    {
        private bool _isExpanded = true;
        private int _expandedWidth = 280;
        private int _collapsedWidth = 60;
        private System.Windows.Forms.Timer _animationTimer;
        private int _targetWidth;
        private bool _isAnimating = false;
        
        // عناصر التحكم
        private Panel _headerPanel;
        private Label _titleLabel;
        private Button _toggleButton;
        private Panel _menuPanel;
        private List<SidebarMenuItem> _menuItems;

        public SimpleSlidingSidebar()
        {
            InitializeComponent();
            SetupAnimation();
        }

        private void InitializeComponent()
        {
            // إعدادات أساسية
            Size = new Size(_expandedWidth, 600);
            BackColor = Color.FromArgb(44, 62, 80);
            Dock = DockStyle.Left;
            
            // تحسين الأداء
            SetStyle(ControlStyles.AllPaintingInWmPaint | 
                     ControlStyles.UserPaint | 
                     ControlStyles.DoubleBuffer | 
                     ControlStyles.ResizeRedraw, true);

            _menuItems = new List<SidebarMenuItem>();
            
            CreateHeader();
            CreateMenuPanel();
        }

        private void CreateHeader()
        {
            _headerPanel = new Panel
            {
                Height = 80,
                Dock = DockStyle.Top,
                BackColor = Color.FromArgb(52, 73, 94),
                Padding = new Padding(15, 10, 15, 10)
            };

            _titleLabel = new Label
            {
                Text = "سمارت لوير",
                Font = new Font("Segoe UI", 14, FontStyle.Bold),
                ForeColor = Color.White,
                AutoSize = false,
                Size = new Size(180, 30),
                Location = new Point(15, 25),
                TextAlign = ContentAlignment.MiddleLeft
            };

            _toggleButton = new Button
            {
                Text = "☰",
                Size = new Size(40, 40),
                Location = new Point(220, 20),
                BackColor = Color.Transparent,
                ForeColor = Color.White,
                FlatStyle = FlatStyle.Flat,
                Font = new Font("Segoe UI", 16, FontStyle.Bold),
                Cursor = Cursors.Hand
            };
            _toggleButton.FlatAppearance.BorderSize = 0;
            _toggleButton.FlatAppearance.MouseOverBackColor = Color.FromArgb(70, 90, 110);
            _toggleButton.Click += ToggleButton_Click;

            _headerPanel.Controls.Add(_titleLabel);
            _headerPanel.Controls.Add(_toggleButton);
            Controls.Add(_headerPanel);
        }

        private void CreateMenuPanel()
        {
            _menuPanel = new Panel
            {
                Dock = DockStyle.Fill,
                BackColor = Color.Transparent,
                AutoScroll = true,
                Padding = new Padding(0, 10, 0, 10)
            };
            Controls.Add(_menuPanel);
        }

        private void SetupAnimation()
        {
            _animationTimer = new System.Windows.Forms.Timer
            {
                Interval = 16 // ~60 FPS
            };
            _animationTimer.Tick += AnimationTimer_Tick;
        }

        // خصائص عامة
        public bool IsExpanded
        {
            get => _isExpanded;
            set
            {
                if (_isExpanded != value)
                {
                    _isExpanded = value;
                    AnimateToState();
                }
            }
        }

        public int ExpandedWidth
        {
            get => _expandedWidth;
            set { _expandedWidth = value; if (_isExpanded) Width = value; }
        }

        public int CollapsedWidth
        {
            get => _collapsedWidth;
            set { _collapsedWidth = value; if (!_isExpanded) Width = value; }
        }

        // إضافة عنصر قائمة
        public void AddMenuItem(string text, string icon, EventHandler clickHandler)
        {
            var item = new SidebarMenuItem(text, icon, clickHandler);
            _menuItems.Add(item);
            _menuPanel.Controls.Add(item);
            UpdateMenuLayout();
        }

        // تحديث تخطيط القائمة
        private void UpdateMenuLayout()
        {
            int yPosition = 10;
            foreach (var item in _menuItems)
            {
                item.Location = new Point(0, yPosition);
                item.Width = _menuPanel.Width;
                yPosition += item.Height + 5;
            }
        }

        // تبديل حالة الشريط الجانبي
        private void ToggleButton_Click(object sender, EventArgs e)
        {
            Toggle();
        }

        public void Toggle()
        {
            IsExpanded = !IsExpanded;
        }

        // الرسوم المتحركة
        private void AnimateToState()
        {
            if (_isAnimating) return;

            _targetWidth = _isExpanded ? _expandedWidth : _collapsedWidth;
            _isAnimating = true;
            _animationTimer.Start();
        }

        private void AnimationTimer_Tick(object sender, EventArgs e)
        {
            int currentWidth = Width;
            int difference = _targetWidth - currentWidth;

            // استخدام منحنى تسارع سلس
            if (Math.Abs(difference) <= 3)
            {
                Width = _targetWidth;
                _isAnimating = false;
                _animationTimer.Stop();
                OnAnimationComplete();
            }
            else
            {
                int step = Math.Max(1, Math.Abs(difference) / 6);
                Width += Math.Sign(difference) * step;
            }

            UpdateUIForCurrentState();
        }

        private void OnAnimationComplete()
        {
            UpdateUIForCurrentState();
            foreach (var item in _menuItems)
            {
                item.UpdateForSidebarState(_isExpanded);
            }
        }

        private void UpdateUIForCurrentState()
        {
            // تحديث زر التبديل
            _toggleButton.Location = new Point(Width - 50, 20);
            
            // تحديث العنوان
            _titleLabel.Visible = Width > 100;
            
            // تحديث عناصر القائمة
            foreach (var item in _menuItems)
            {
                item.Width = Width;
                item.UpdateForSidebarState(Width > 100);
            }
        }

        protected override void OnResize(EventArgs e)
        {
            base.OnResize(e);
            UpdateMenuLayout();
        }

        protected override void OnPaint(PaintEventArgs e)
        {
            base.OnPaint(e);
            
            Graphics g = e.Graphics;
            g.SmoothingMode = SmoothingMode.AntiAlias;

            // رسم ظل أنيق على الجانب الأيمن
            if (Width > _collapsedWidth + 10)
            {
                Rectangle shadowRect = new Rectangle(Width - 3, 0, 3, Height);
                using (var shadowBrush = new LinearGradientBrush(shadowRect,
                    Color.FromArgb(20, 0, 0, 0),
                    Color.Transparent,
                    LinearGradientMode.Horizontal))
                {
                    g.FillRectangle(shadowBrush, shadowRect);
                }
            }
        }
    }

    // عنصر في الشريط الجانبي
    public class SidebarMenuItem : Panel
    {
        private string _text;
        private string _icon;
        private EventHandler _clickHandler;
        private bool _isHovered = false;
        
        private Label _iconLabel;
        private Label _textLabel;

        public SidebarMenuItem(string text, string icon, EventHandler clickHandler)
        {
            _text = text;
            _icon = icon;
            _clickHandler = clickHandler;
            
            InitializeComponent();
        }

        private void InitializeComponent()
        {
            Height = 50;
            BackColor = Color.Transparent;
            Cursor = Cursors.Hand;
            Padding = new Padding(15, 10, 15, 10);

            // تحسين الأداء
            SetStyle(ControlStyles.AllPaintingInWmPaint | 
                     ControlStyles.UserPaint | 
                     ControlStyles.DoubleBuffer | 
                     ControlStyles.ResizeRedraw, true);

            CreateControls();
            AttachEvents();
        }

        private void CreateControls()
        {
            _iconLabel = new Label
            {
                Text = _icon,
                Font = new Font("Segoe UI", 16, FontStyle.Regular),
                ForeColor = Color.White,
                Size = new Size(30, 30),
                Location = new Point(15, 10),
                TextAlign = ContentAlignment.MiddleCenter,
                BackColor = Color.Transparent
            };

            _textLabel = new Label
            {
                Text = _text,
                Font = new Font("Segoe UI", 11, FontStyle.Regular),
                ForeColor = Color.White,
                Size = new Size(150, 30),
                Location = new Point(55, 10),
                TextAlign = ContentAlignment.MiddleLeft,
                BackColor = Color.Transparent
            };

            Controls.Add(_iconLabel);
            Controls.Add(_textLabel);
        }

        private void AttachEvents()
        {
            Click += OnItemClick;
            _iconLabel.Click += OnItemClick;
            _textLabel.Click += OnItemClick;
            
            MouseEnter += OnMouseEnter;
            MouseLeave += OnMouseLeave;
            _iconLabel.MouseEnter += OnMouseEnter;
            _iconLabel.MouseLeave += OnMouseLeave;
            _textLabel.MouseEnter += OnMouseEnter;
            _textLabel.MouseLeave += OnMouseLeave;
        }

        public void UpdateForSidebarState(bool isExpanded)
        {
            _textLabel.Visible = isExpanded;
            if (isExpanded)
            {
                _iconLabel.Location = new Point(15, 10);
                _textLabel.Location = new Point(55, 10);
            }
            else
            {
                _iconLabel.Location = new Point((Width - 30) / 2, 10);
            }
        }

        private void OnItemClick(object sender, EventArgs e)
        {
            _clickHandler?.Invoke(this, e);
        }

        private void OnMouseEnter(object sender, EventArgs e)
        {
            _isHovered = true;
            Invalidate();
        }

        private void OnMouseLeave(object sender, EventArgs e)
        {
            _isHovered = false;
            Invalidate();
        }

        protected override void OnPaint(PaintEventArgs e)
        {
            Graphics g = e.Graphics;
            g.SmoothingMode = SmoothingMode.AntiAlias;

            Rectangle rect = new Rectangle(5, 0, Width - 10, Height - 2);
            
            if (_isHovered)
            {
                using (var brush = new SolidBrush(Color.FromArgb(60, 255, 255, 255)))
                using (var path = GetRoundedRectangle(rect, 8))
                {
                    g.FillPath(brush, path);
                }
            }
        }

        private GraphicsPath GetRoundedRectangle(Rectangle rect, int radius)
        {
            GraphicsPath path = new GraphicsPath();
            path.AddArc(rect.X, rect.Y, radius, radius, 180, 90);
            path.AddArc(rect.X + rect.Width - radius, rect.Y, radius, radius, 270, 90);
            path.AddArc(rect.X + rect.Width - radius, rect.Y + rect.Height - radius, radius, radius, 0, 90);
            path.AddArc(rect.X, rect.Y + rect.Height - radius, radius, radius, 90, 90);
            path.CloseAllFigures();
            return path;
        }
    }
}
