using System;
using System.Data.SQLite;
using System.IO;

namespace SmartLawyer.Database
{
    public class DatabaseManager
    {
        private static DatabaseManager _instance;
        private static readonly object _lock = new object();
        private string _connectionString;

        private DatabaseManager()
        {
            string dbPath = Path.Combine(AppDomain.CurrentDomain.BaseDirectory, "SmartLawyer.db");
            _connectionString = $"Data Source={dbPath};Version=3;";
            InitializeDatabase();
        }

        public static DatabaseManager Instance
        {
            get
            {
                if (_instance == null)
                {
                    lock (_lock)
                    {
                        if (_instance == null)
                            _instance = new DatabaseManager();
                    }
                }
                return _instance;
            }
        }

        public SQLiteConnection GetConnection()
        {
            return new SQLiteConnection(_connectionString);
        }

        private void InitializeDatabase()
        {
            using (var connection = GetConnection())
            {
                connection.Open();
                CreateTables(connection);
            }
        }

        private void CreateTables(SQLiteConnection connection)
        {
            // جدول المستخدمين
            string createUsersTable = @"
                CREATE TABLE IF NOT EXISTS Users (
                    Id INTEGER PRIMARY KEY AUTOINCREMENT,
                    Username TEXT UNIQUE NOT NULL,
                    Password TEXT NOT NULL,
                    FullName TEXT NOT NULL,
                    Email TEXT,
                    Phone TEXT,
                    Role TEXT NOT NULL DEFAULT 'User',
                    IsActive INTEGER DEFAULT 1,
                    CreatedDate DATETIME DEFAULT CURRENT_TIMESTAMP,
                    LastLogin DATETIME
                )";

            // جدول الموكلين
            string createClientsTable = @"
                CREATE TABLE IF NOT EXISTS Clients (
                    Id INTEGER PRIMARY KEY AUTOINCREMENT,
                    ClientType TEXT NOT NULL, -- Individual, Company, Administration
                    FirstName TEXT,
                    LastName TEXT,
                    CompanyName TEXT,
                    CIN TEXT,
                    ICE TEXT,
                    Address TEXT,
                    City TEXT,
                    Phone TEXT,
                    Email TEXT,
                    Notes TEXT,
                    CreatedDate DATETIME DEFAULT CURRENT_TIMESTAMP,
                    CreatedBy INTEGER,
                    FOREIGN KEY (CreatedBy) REFERENCES Users(Id)
                )";

            // جدول الملفات والقضايا
            string createCasesTable = @"
                CREATE TABLE IF NOT EXISTS Cases (
                    Id INTEGER PRIMARY KEY AUTOINCREMENT,
                    CaseNumber TEXT UNIQUE NOT NULL,
                    Title TEXT NOT NULL,
                    CaseType TEXT NOT NULL, -- Civil, Commercial, Criminal, Administrative
                    ClientId INTEGER NOT NULL,
                    Court TEXT,
                    Judge TEXT,
                    Status TEXT DEFAULT 'Open', -- Open, Closed, Suspended
                    Description TEXT,
                    StartDate DATE,
                    EndDate DATE,
                    CreatedDate DATETIME DEFAULT CURRENT_TIMESTAMP,
                    CreatedBy INTEGER,
                    FOREIGN KEY (ClientId) REFERENCES Clients(Id),
                    FOREIGN KEY (CreatedBy) REFERENCES Users(Id)
                )";

            // جدول الجلسات
            string createHearingsTable = @"
                CREATE TABLE IF NOT EXISTS Hearings (
                    Id INTEGER PRIMARY KEY AUTOINCREMENT,
                    CaseId INTEGER NOT NULL,
                    HearingDate DATETIME NOT NULL,
                    Court TEXT NOT NULL,
                    Room TEXT,
                    Judge TEXT,
                    HearingType TEXT, -- Initial, Follow-up, Final
                    Status TEXT DEFAULT 'Scheduled', -- Scheduled, Completed, Postponed, Cancelled
                    Notes TEXT,
                    Reminder INTEGER DEFAULT 1,
                    CreatedDate DATETIME DEFAULT CURRENT_TIMESTAMP,
                    CreatedBy INTEGER,
                    FOREIGN KEY (CaseId) REFERENCES Cases(Id),
                    FOREIGN KEY (CreatedBy) REFERENCES Users(Id)
                )";

            // جدول المواعيد
            string createAppointmentsTable = @"
                CREATE TABLE IF NOT EXISTS Appointments (
                    Id INTEGER PRIMARY KEY AUTOINCREMENT,
                    ClientId INTEGER,
                    Title TEXT NOT NULL,
                    Description TEXT,
                    AppointmentDate DATETIME NOT NULL,
                    Location TEXT,
                    Status TEXT DEFAULT 'Scheduled', -- Scheduled, Completed, Cancelled
                    Reminder INTEGER DEFAULT 1,
                    CreatedDate DATETIME DEFAULT CURRENT_TIMESTAMP,
                    CreatedBy INTEGER,
                    FOREIGN KEY (ClientId) REFERENCES Clients(Id),
                    FOREIGN KEY (CreatedBy) REFERENCES Users(Id)
                )";

            // جدول الإجراءات
            string createProceduresTable = @"
                CREATE TABLE IF NOT EXISTS Procedures (
                    Id INTEGER PRIMARY KEY AUTOINCREMENT,
                    CaseId INTEGER NOT NULL,
                    ProcedureType TEXT NOT NULL, -- Summons, Notification, Expertise, Appeal
                    Description TEXT NOT NULL,
                    ProcedureDate DATE NOT NULL,
                    Location TEXT,
                    Status TEXT DEFAULT 'Pending', -- Pending, Completed, Failed
                    Cost DECIMAL(10,2) DEFAULT 0,
                    Notes TEXT,
                    CreatedDate DATETIME DEFAULT CURRENT_TIMESTAMP,
                    CreatedBy INTEGER,
                    FOREIGN KEY (CaseId) REFERENCES Cases(Id),
                    FOREIGN KEY (CreatedBy) REFERENCES Users(Id)
                )";

            // جدول المصاريف
            string createExpensesTable = @"
                CREATE TABLE IF NOT EXISTS Expenses (
                    Id INTEGER PRIMARY KEY AUTOINCREMENT,
                    CaseId INTEGER,
                    ExpenseType TEXT NOT NULL, -- Court Fees, Printing, Transport, Other
                    Description TEXT NOT NULL,
                    Amount DECIMAL(10,2) NOT NULL,
                    ExpenseDate DATE NOT NULL,
                    Receipt TEXT, -- Path to receipt file
                    Notes TEXT,
                    CreatedDate DATETIME DEFAULT CURRENT_TIMESTAMP,
                    CreatedBy INTEGER,
                    FOREIGN KEY (CaseId) REFERENCES Cases(Id),
                    FOREIGN KEY (CreatedBy) REFERENCES Users(Id)
                )";

            // جدول المستندات
            string createDocumentsTable = @"
                CREATE TABLE IF NOT EXISTS Documents (
                    Id INTEGER PRIMARY KEY AUTOINCREMENT,
                    CaseId INTEGER,
                    ClientId INTEGER,
                    DocumentName TEXT NOT NULL,
                    DocumentType TEXT, -- PDF, Word, Image, Other
                    FilePath TEXT NOT NULL,
                    FileSize INTEGER,
                    Description TEXT,
                    UploadDate DATETIME DEFAULT CURRENT_TIMESTAMP,
                    UploadedBy INTEGER,
                    FOREIGN KEY (CaseId) REFERENCES Cases(Id),
                    FOREIGN KEY (ClientId) REFERENCES Clients(Id),
                    FOREIGN KEY (UploadedBy) REFERENCES Users(Id)
                )";

            // جدول سجل النشاطات
            string createActivityLogTable = @"
                CREATE TABLE IF NOT EXISTS ActivityLog (
                    Id INTEGER PRIMARY KEY AUTOINCREMENT,
                    UserId INTEGER NOT NULL,
                    Action TEXT NOT NULL,
                    TableName TEXT,
                    RecordId INTEGER,
                    OldValues TEXT,
                    NewValues TEXT,
                    Timestamp DATETIME DEFAULT CURRENT_TIMESTAMP,
                    FOREIGN KEY (UserId) REFERENCES Users(Id)
                )";

            // جدول الإعدادات
            string createSettingsTable = @"
                CREATE TABLE IF NOT EXISTS Settings (
                    Id INTEGER PRIMARY KEY AUTOINCREMENT,
                    SettingKey TEXT UNIQUE NOT NULL,
                    SettingValue TEXT,
                    Description TEXT,
                    UpdatedDate DATETIME DEFAULT CURRENT_TIMESTAMP,
                    UpdatedBy INTEGER,
                    FOREIGN KEY (UpdatedBy) REFERENCES Users(Id)
                )";

            // تنفيذ إنشاء الجداول
            ExecuteNonQuery(connection, createUsersTable);
            ExecuteNonQuery(connection, createClientsTable);
            ExecuteNonQuery(connection, createCasesTable);
            ExecuteNonQuery(connection, createHearingsTable);
            ExecuteNonQuery(connection, createAppointmentsTable);
            ExecuteNonQuery(connection, createProceduresTable);
            ExecuteNonQuery(connection, createExpensesTable);
            ExecuteNonQuery(connection, createDocumentsTable);
            ExecuteNonQuery(connection, createActivityLogTable);
            ExecuteNonQuery(connection, createSettingsTable);

            // إدراج البيانات الافتراضية
            InsertDefaultData(connection);
        }

        private void ExecuteNonQuery(SQLiteConnection connection, string sql)
        {
            using (var command = new SQLiteCommand(sql, connection))
            {
                command.ExecuteNonQuery();
            }
        }

        private void InsertDefaultData(SQLiteConnection connection)
        {
            // إنشاء مستخدم افتراضي (admin)
            string checkAdmin = "SELECT COUNT(*) FROM Users WHERE Username = 'admin'";
            using (var command = new SQLiteCommand(checkAdmin, connection))
            {
                int count = Convert.ToInt32(command.ExecuteScalar());
                if (count == 0)
                {
                    string insertAdmin = @"
                        INSERT INTO Users (Username, Password, FullName, Role, Email) 
                        VALUES ('admin', 'admin123', 'مدير النظام', 'Admin', '<EMAIL>')";
                    ExecuteNonQuery(connection, insertAdmin);
                }
            }

            // إدراج الإعدادات الافتراضية
            string[] defaultSettings = {
                "INSERT OR IGNORE INTO Settings (SettingKey, SettingValue, Description) VALUES ('Language', 'ar', 'لغة النظام الافتراضية')",
                "INSERT OR IGNORE INTO Settings (SettingKey, SettingValue, Description) VALUES ('CompanyName', 'مكتب المحاماة', 'اسم المكتب')",
                "INSERT OR IGNORE INTO Settings (SettingKey, SettingValue, Description) VALUES ('CompanyAddress', '', 'عنوان المكتب')",
                "INSERT OR IGNORE INTO Settings (SettingKey, SettingValue, Description) VALUES ('CompanyPhone', '', 'هاتف المكتب')",
                "INSERT OR IGNORE INTO Settings (SettingKey, SettingValue, Description) VALUES ('CompanyEmail', '', 'بريد المكتب الإلكتروني')",
                "INSERT OR IGNORE INTO Settings (SettingKey, SettingValue, Description) VALUES ('BackupPath', '', 'مسار النسخ الاحتياطي')",
                "INSERT OR IGNORE INTO Settings (SettingKey, SettingValue, Description) VALUES ('AutoBackup', '1', 'النسخ الاحتياطي التلقائي')",
                "INSERT OR IGNORE INTO Settings (SettingKey, SettingValue, Description) VALUES ('EmailNotifications', '1', 'إشعارات البريد الإلكتروني')",
                "INSERT OR IGNORE INTO Settings (SettingKey, SettingValue, Description) VALUES ('WhatsAppNotifications', '0', 'إشعارات واتساب')"
            };

            foreach (string setting in defaultSettings)
            {
                ExecuteNonQuery(connection, setting);
            }
        }
    }
}
