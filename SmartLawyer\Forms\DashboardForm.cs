using System;
using System.Drawing;
using System.Drawing.Drawing2D;
using System.Windows.Forms;
using SmartLawyer.Database;
using SmartLawyer.UI;
using SmartLawyer.Localization;

namespace SmartLawyer.Forms
{
    public partial class DashboardForm : Form
    {
        private Panel mainPanel;
        private Panel statsPanel;
        private Panel chartsPanel;
        private Panel recentActivitiesPanel;
        private Panel quickActionsPanel;

        public DashboardForm()
        {
            InitializeComponent();
            LoadDashboardData();
        }

        private void InitializeComponent()
        {
            // إعدادات النافذة الأساسية
            Text = "لوحة المعلومات - سمارت لوير";
            Size = new Size(1200, 800);
            StartPosition = FormStartPosition.CenterScreen;
            BackColor = ThemeManager.Instance.GetBackgroundColor();
            Font = new Font("Segoe UI", 10);

            CreateMainLayout();
            CreateStatsCards();
            CreateChartsSection();
            CreateRecentActivities();
            CreateQuickActions();
        }

        private void CreateMainLayout()
        {
            mainPanel = new Panel
            {
                Dock = DockStyle.Fill,
                Padding = new Padding(20),
                BackColor = Color.Transparent,
                AutoScroll = true
            };

            // عنوان الصفحة
            var titleLabel = new Label
            {
                Text = "لوحة المعلومات",
                Font = new Font("Segoe UI", 24, FontStyle.Bold),
                ForeColor = ThemeManager.Instance.GetPrimaryColor(),
                AutoSize = true,
                Location = new Point(20, 20)
            };

            var subtitleLabel = new Label
            {
                Text = "نظرة عامة على أداء المكتب",
                Font = new Font("Segoe UI", 12),
                ForeColor = Color.Gray,
                AutoSize = true,
                Location = new Point(20, 60)
            };

            mainPanel.Controls.Add(titleLabel);
            mainPanel.Controls.Add(subtitleLabel);
            Controls.Add(mainPanel);
        }

        private void CreateStatsCards()
        {
            statsPanel = new Panel
            {
                Size = new Size(1140, 150),
                Location = new Point(20, 100),
                BackColor = Color.Transparent
            };

            // بطاقات الإحصائيات
            var clientsCard = CreateStatsCard("إجمالي الموكلين", GetClientsCount().ToString(), "👥", Color.FromArgb(52, 152, 219));
            var casesCard = CreateStatsCard("القضايا النشطة", GetActiveCasesCount().ToString(), "⚖️", Color.FromArgb(46, 204, 113));
            var hearingsCard = CreateStatsCard("الجلسات هذا الشهر", GetMonthlyHearingsCount().ToString(), "📅", Color.FromArgb(155, 89, 182));
            var revenueCard = CreateStatsCard("الإيرادات الشهرية", GetMonthlyRevenue().ToString("C"), "💰", Color.FromArgb(230, 126, 34));

            clientsCard.Location = new Point(0, 0);
            casesCard.Location = new Point(290, 0);
            hearingsCard.Location = new Point(580, 0);
            revenueCard.Location = new Point(870, 0);

            statsPanel.Controls.AddRange(new Control[] { clientsCard, casesCard, hearingsCard, revenueCard });
            mainPanel.Controls.Add(statsPanel);
        }

        private Panel CreateStatsCard(string title, string value, string icon, Color accentColor)
        {
            var card = new Panel
            {
                Size = new Size(270, 130),
                BackColor = Color.White,
                Cursor = Cursors.Hand
            };

            // تحسين الرسم
            card.Paint += (s, e) =>
            {
                var g = e.Graphics;
                g.SmoothingMode = SmoothingMode.AntiAlias;

                // رسم الخلفية مع الحواف الدائرية
                using (var path = GetRoundedRectangle(new Rectangle(0, 0, card.Width, card.Height), 15))
                using (var brush = new SolidBrush(Color.White))
                {
                    g.FillPath(brush, path);
                }

                // رسم الشريط الملون
                using (var accentBrush = new SolidBrush(accentColor))
                {
                    g.FillRectangle(accentBrush, 0, 0, 5, card.Height);
                }

                // رسم الظل
                using (var shadowBrush = new SolidBrush(Color.FromArgb(20, 0, 0, 0)))
                {
                    g.FillRectangle(shadowBrush, 2, 2, card.Width, card.Height);
                }
            };

            // الأيقونة
            var iconLabel = new Label
            {
                Text = icon,
                Font = new Font("Segoe UI Emoji", 24),
                ForeColor = accentColor,
                Size = new Size(50, 50),
                Location = new Point(200, 20),
                TextAlign = ContentAlignment.MiddleCenter,
                BackColor = Color.Transparent
            };

            // العنوان
            var titleLabel = new Label
            {
                Text = title,
                Font = new Font("Segoe UI", 11, FontStyle.Regular),
                ForeColor = Color.Gray,
                Size = new Size(180, 25),
                Location = new Point(20, 25),
                TextAlign = ContentAlignment.MiddleLeft,
                BackColor = Color.Transparent
            };

            // القيمة
            var valueLabel = new Label
            {
                Text = value,
                Font = new Font("Segoe UI", 20, FontStyle.Bold),
                ForeColor = ThemeManager.Instance.GetPrimaryColor(),
                Size = new Size(180, 40),
                Location = new Point(20, 55),
                TextAlign = ContentAlignment.MiddleLeft,
                BackColor = Color.Transparent
            };

            // تأثير الـ hover
            card.MouseEnter += (s, e) => card.BackColor = Color.FromArgb(248, 249, 250);
            card.MouseLeave += (s, e) => card.BackColor = Color.White;

            card.Controls.AddRange(new Control[] { iconLabel, titleLabel, valueLabel });
            return card;
        }

        private void CreateChartsSection()
        {
            chartsPanel = new Panel
            {
                Size = new Size(1140, 300),
                Location = new Point(20, 270),
                BackColor = Color.White
            };

            chartsPanel.Paint += (s, e) =>
            {
                var g = e.Graphics;
                g.SmoothingMode = SmoothingMode.AntiAlias;
                using (var path = GetRoundedRectangle(new Rectangle(0, 0, chartsPanel.Width, chartsPanel.Height), 15))
                using (var brush = new SolidBrush(Color.White))
                {
                    g.FillPath(brush, path);
                }
            };

            var chartTitle = new Label
            {
                Text = "إحصائيات الأداء الشهري",
                Font = new Font("Segoe UI", 16, FontStyle.Bold),
                ForeColor = ThemeManager.Instance.GetPrimaryColor(),
                Location = new Point(20, 20),
                AutoSize = true,
                BackColor = Color.Transparent
            };

            // هنا يمكن إضافة مخططات بيانية لاحقاً
            var chartPlaceholder = new Label
            {
                Text = "📊 سيتم إضافة المخططات البيانية قريباً",
                Font = new Font("Segoe UI", 14),
                ForeColor = Color.Gray,
                Size = new Size(400, 200),
                Location = new Point(370, 100),
                TextAlign = ContentAlignment.MiddleCenter,
                BackColor = Color.Transparent
            };

            chartsPanel.Controls.AddRange(new Control[] { chartTitle, chartPlaceholder });
            mainPanel.Controls.Add(chartsPanel);
        }

        private void CreateRecentActivities()
        {
            recentActivitiesPanel = new Panel
            {
                Size = new Size(560, 250),
                Location = new Point(20, 590),
                BackColor = Color.White
            };

            recentActivitiesPanel.Paint += (s, e) =>
            {
                var g = e.Graphics;
                g.SmoothingMode = SmoothingMode.AntiAlias;
                using (var path = GetRoundedRectangle(new Rectangle(0, 0, recentActivitiesPanel.Width, recentActivitiesPanel.Height), 15))
                using (var brush = new SolidBrush(Color.White))
                {
                    g.FillPath(brush, path);
                }
            };

            var activitiesTitle = new Label
            {
                Text = "الأنشطة الأخيرة",
                Font = new Font("Segoe UI", 16, FontStyle.Bold),
                ForeColor = ThemeManager.Instance.GetPrimaryColor(),
                Location = new Point(20, 20),
                AutoSize = true,
                BackColor = Color.Transparent
            };

            recentActivitiesPanel.Controls.Add(activitiesTitle);
            mainPanel.Controls.Add(recentActivitiesPanel);
        }

        private void CreateQuickActions()
        {
            quickActionsPanel = new Panel
            {
                Size = new Size(560, 250),
                Location = new Point(600, 590),
                BackColor = Color.White
            };

            quickActionsPanel.Paint += (s, e) =>
            {
                var g = e.Graphics;
                g.SmoothingMode = SmoothingMode.AntiAlias;
                using (var path = GetRoundedRectangle(new Rectangle(0, 0, quickActionsPanel.Width, quickActionsPanel.Height), 15))
                using (var brush = new SolidBrush(Color.White))
                {
                    g.FillPath(brush, path);
                }
            };

            var actionsTitle = new Label
            {
                Text = "إجراءات سريعة",
                Font = new Font("Segoe UI", 16, FontStyle.Bold),
                ForeColor = ThemeManager.Instance.GetPrimaryColor(),
                Location = new Point(20, 20),
                AutoSize = true,
                BackColor = Color.Transparent
            };

            // أزرار الإجراءات السريعة
            var addClientBtn = CreateQuickActionButton("إضافة موكل جديد", "👤", Color.FromArgb(52, 152, 219));
            var addCaseBtn = CreateQuickActionButton("إضافة قضية جديدة", "📁", Color.FromArgb(46, 204, 113));
            var scheduleHearingBtn = CreateQuickActionButton("جدولة جلسة", "📅", Color.FromArgb(155, 89, 182));

            addClientBtn.Location = new Point(20, 60);
            addCaseBtn.Location = new Point(190, 60);
            scheduleHearingBtn.Location = new Point(360, 60);

            quickActionsPanel.Controls.AddRange(new Control[] { actionsTitle, addClientBtn, addCaseBtn, scheduleHearingBtn });
            mainPanel.Controls.Add(quickActionsPanel);
        }

        private Button CreateQuickActionButton(string text, string icon, Color color)
        {
            var button = new Button
            {
                Size = new Size(150, 80),
                FlatStyle = FlatStyle.Flat,
                BackColor = color,
                ForeColor = Color.White,
                Font = new Font("Segoe UI", 10, FontStyle.Bold),
                Text = $"{icon}\n{text}",
                TextAlign = ContentAlignment.MiddleCenter,
                Cursor = Cursors.Hand
            };

            button.FlatAppearance.BorderSize = 0;
            button.FlatAppearance.MouseOverBackColor = ControlPaint.Light(color, 0.2f);
            button.FlatAppearance.MouseDownBackColor = ControlPaint.Dark(color, 0.1f);

            return button;
        }

        private GraphicsPath GetRoundedRectangle(Rectangle rect, int radius)
        {
            GraphicsPath path = new GraphicsPath();
            path.AddArc(rect.X, rect.Y, radius, radius, 180, 90);
            path.AddArc(rect.X + rect.Width - radius, rect.Y, radius, radius, 270, 90);
            path.AddArc(rect.X + rect.Width - radius, rect.Y + rect.Height - radius, radius, radius, 0, 90);
            path.AddArc(rect.X, rect.Y + rect.Height - radius, radius, radius, 90, 90);
            path.CloseAllFigures();
            return path;
        }

        private void LoadDashboardData()
        {
            // تحديث البيانات من قاعدة البيانات
            Refresh();
        }

        // دوال جلب البيانات من قاعدة البيانات
        private int GetClientsCount()
        {
            try
            {
                using (var connection = DatabaseManager.Instance.GetConnection())
                {
                    connection.Open();
                    using (var command = connection.CreateCommand())
                    {
                        command.CommandText = "SELECT COUNT(*) FROM Clients";
                        return Convert.ToInt32(command.ExecuteScalar());
                    }
                }
            }
            catch
            {
                return 0;
            }
        }

        private int GetActiveCasesCount()
        {
            try
            {
                using (var connection = DatabaseManager.Instance.GetConnection())
                {
                    connection.Open();
                    using (var command = connection.CreateCommand())
                    {
                        command.CommandText = "SELECT COUNT(*) FROM Cases WHERE Status = 'نشط'";
                        return Convert.ToInt32(command.ExecuteScalar());
                    }
                }
            }
            catch
            {
                return 0;
            }
        }

        private int GetMonthlyHearingsCount()
        {
            try
            {
                using (var connection = DatabaseManager.Instance.GetConnection())
                {
                    connection.Open();
                    using (var command = connection.CreateCommand())
                    {
                        command.CommandText = "SELECT COUNT(*) FROM Hearings WHERE strftime('%Y-%m', HearingDate) = strftime('%Y-%m', 'now')";
                        return Convert.ToInt32(command.ExecuteScalar());
                    }
                }
            }
            catch
            {
                return 0;
            }
        }

        private decimal GetMonthlyRevenue()
        {
            try
            {
                using (var connection = DatabaseManager.Instance.GetConnection())
                {
                    connection.Open();
                    using (var command = connection.CreateCommand())
                    {
                        command.CommandText = "SELECT COALESCE(SUM(Amount), 0) FROM Expenses WHERE ExpenseType = 'إيراد' AND strftime('%Y-%m', Date) = strftime('%Y-%m', 'now')";
                        var result = command.ExecuteScalar();
                        return result != DBNull.Value ? Convert.ToDecimal(result) : 0;
                    }
                }
            }
            catch
            {
                return 0;
            }
        }
    }
}
