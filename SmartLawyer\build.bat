@echo off
title Smart Lawyer - بناء المشروع
echo.
echo ========================================
echo    Smart Lawyer - سمارت لوير
echo    بناء وتجميع المشروع
echo ========================================
echo    تطوير: Generation Five
echo    هاتف: +212 661 77 53 72
echo    الموقع: www.generationfive.net
echo ========================================
echo.

REM التحقق من وجود .NET SDK
dotnet --version >nul 2>&1
if errorlevel 1 (
    echo خطأ: .NET SDK غير مثبت على النظام
    echo يرجى تحميل وتثبيت .NET 6.0 SDK من:
    echo https://dotnet.microsoft.com/download/dotnet/6.0
    pause
    exit /b 1
)

echo جاري تنظيف المشروع...
dotnet clean
if errorlevel 1 (
    echo خطأ في تنظيف المشروع
    pause
    exit /b 1
)

echo.
echo جاري استعادة الحزم...
dotnet restore
if errorlevel 1 (
    echo خطأ في استعادة الحزم
    pause
    exit /b 1
)

echo.
echo جاري بناء المشروع...
dotnet build --configuration Release
if errorlevel 1 (
    echo خطأ في بناء المشروع
    pause
    exit /b 1
)

echo.
echo جاري إنشاء ملف تنفيذي مستقل...
dotnet publish --configuration Release --self-contained true --runtime win-x64 --output "bin\Release\Publish"
if errorlevel 1 (
    echo خطأ في إنشاء الملف التنفيذي
    pause
    exit /b 1
)

echo.
echo ========================================
echo تم بناء المشروع بنجاح!
echo ========================================
echo.
echo الملفات المبنية موجودة في:
echo bin\Release\net6.0-windows\
echo.
echo الملف التنفيذي المستقل موجود في:
echo bin\Release\Publish\
echo.
echo يمكنك الآن تشغيل البرنامج باستخدام:
echo SmartLawyer.exe
echo.
pause
