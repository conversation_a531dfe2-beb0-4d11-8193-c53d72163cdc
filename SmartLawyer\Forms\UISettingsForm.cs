using System;
using System.Drawing;
using System.Windows.Forms;
using SmartLawyer.Localization;
using SmartLawyer.UI;

namespace SmartLawyer.Forms
{
    public partial class UISettingsForm : Form
    {
        private ModernPanel mainPanel;
        private ModernPanel themePanel;
        private ModernPanel languagePanel;
        private ModernPanel animationPanel;
        private ModernPanel fontPanel;
        
        private ComboBox cmbTheme;
        private ComboBox cmbLanguage;
        private CheckBox chkEnableAnimations;
        private TrackBar trackAnimationSpeed;
        private CheckBox chkEnableShadows;
        private TrackBar trackBorderRadius;
        private ComboBox cmbFontFamily;
        private TrackBar trackFontSize;
        private CheckBox chkBoldHeaders;
        
        private ModernButton btnSave;
        private ModernButton btnCancel;
        private ModernButton btnReset;
        private ModernButton btnPreview;

        public UISettingsForm()
        {
            InitializeComponent();
            LoadCurrentSettings();
            LoadLanguage();
            ApplyTheme();
        }

        private void InitializeComponent()
        {
            this.SuspendLayout();

            // إعداد النافذة
            this.Text = "إعدادات الواجهة";
            this.Size = new Size(800, 600);
            this.StartPosition = FormStartPosition.CenterParent;
            this.FormBorderStyle = FormBorderStyle.FixedDialog;
            this.MaximizeBox = false;
            this.MinimizeBox = false;
            this.BackColor = ThemeManager.Instance.GetBackgroundColor();

            // اللوحة الرئيسية
            mainPanel = new ModernPanel
            {
                Dock = DockStyle.Fill,
                Padding = new Padding(20),
                BackColor = ThemeManager.Instance.GetSurfaceColor(),
                BorderRadius = 10
            };

            CreateThemeSection();
            CreateLanguageSection();
            CreateAnimationSection();
            CreateFontSection();
            CreateActionButtons();

            this.Controls.Add(mainPanel);
            this.ResumeLayout(false);
        }

        private void CreateThemeSection()
        {
            themePanel = new ModernPanel
            {
                Size = new Size(740, 120),
                Location = new Point(20, 20),
                BackColor = ThemeManager.Instance.GetSurfaceColor(),
                BorderRadius = 8
            };

            Label lblThemeTitle = new Label
            {
                Text = "إعدادات الثيم",
                Location = new Point(15, 15),
                Size = new Size(200, 25),
                Font = new Font("Segoe UI", 12, FontStyle.Bold),
                ForeColor = ThemeManager.Instance.GetTextColor()
            };

            Label lblTheme = new Label
            {
                Text = "الثيم:",
                Location = new Point(15, 50),
                Size = new Size(80, 25),
                Font = new Font("Segoe UI", 10),
                ForeColor = ThemeManager.Instance.GetTextColor()
            };

            cmbTheme = new ComboBox
            {
                Location = new Point(100, 50),
                Size = new Size(150, 25),
                DropDownStyle = ComboBoxStyle.DropDownList
            };
            cmbTheme.Items.AddRange(new[] { "فاتح", "داكن", "أزرق", "أخضر" });
            cmbTheme.SelectedIndexChanged += CmbTheme_SelectedIndexChanged;

            chkEnableShadows = new CheckBox
            {
                Text = "تفعيل الظلال",
                Location = new Point(300, 50),
                Size = new Size(120, 25),
                Font = new Font("Segoe UI", 10),
                ForeColor = ThemeManager.Instance.GetTextColor()
            };

            Label lblBorderRadius = new Label
            {
                Text = "انحناء الحواف:",
                Location = new Point(450, 50),
                Size = new Size(100, 25),
                Font = new Font("Segoe UI", 10),
                ForeColor = ThemeManager.Instance.GetTextColor()
            };

            trackBorderRadius = new TrackBar
            {
                Location = new Point(560, 45),
                Size = new Size(150, 35),
                Minimum = 0,
                Maximum = 20,
                TickFrequency = 5
            };

            themePanel.Controls.AddRange(new Control[] { 
                lblThemeTitle, lblTheme, cmbTheme, chkEnableShadows, lblBorderRadius, trackBorderRadius 
            });
            mainPanel.Controls.Add(themePanel);
        }

        private void CreateLanguageSection()
        {
            languagePanel = new ModernPanel
            {
                Size = new Size(740, 80),
                Location = new Point(20, 160),
                BackColor = ThemeManager.Instance.GetSurfaceColor(),
                BorderRadius = 8
            };

            Label lblLanguageTitle = new Label
            {
                Text = "إعدادات اللغة",
                Location = new Point(15, 15),
                Size = new Size(200, 25),
                Font = new Font("Segoe UI", 12, FontStyle.Bold),
                ForeColor = ThemeManager.Instance.GetTextColor()
            };

            Label lblLanguage = new Label
            {
                Text = "اللغة:",
                Location = new Point(15, 50),
                Size = new Size(80, 25),
                Font = new Font("Segoe UI", 10),
                ForeColor = ThemeManager.Instance.GetTextColor()
            };

            cmbLanguage = new ComboBox
            {
                Location = new Point(100, 50),
                Size = new Size(150, 25),
                DropDownStyle = ComboBoxStyle.DropDownList
            };
            cmbLanguage.Items.AddRange(new[] { "العربية", "Français" });
            cmbLanguage.SelectedIndexChanged += CmbLanguage_SelectedIndexChanged;

            languagePanel.Controls.AddRange(new Control[] { 
                lblLanguageTitle, lblLanguage, cmbLanguage 
            });
            mainPanel.Controls.Add(languagePanel);
        }

        private void CreateAnimationSection()
        {
            animationPanel = new ModernPanel
            {
                Size = new Size(740, 120),
                Location = new Point(20, 260),
                BackColor = ThemeManager.Instance.GetSurfaceColor(),
                BorderRadius = 8
            };

            Label lblAnimationTitle = new Label
            {
                Text = "إعدادات الحركة",
                Location = new Point(15, 15),
                Size = new Size(200, 25),
                Font = new Font("Segoe UI", 12, FontStyle.Bold),
                ForeColor = ThemeManager.Instance.GetTextColor()
            };

            chkEnableAnimations = new CheckBox
            {
                Text = "تفعيل الحركات",
                Location = new Point(15, 50),
                Size = new Size(120, 25),
                Font = new Font("Segoe UI", 10),
                ForeColor = ThemeManager.Instance.GetTextColor()
            };
            chkEnableAnimations.CheckedChanged += ChkEnableAnimations_CheckedChanged;

            Label lblAnimationSpeed = new Label
            {
                Text = "سرعة الحركة:",
                Location = new Point(200, 50),
                Size = new Size(100, 25),
                Font = new Font("Segoe UI", 10),
                ForeColor = ThemeManager.Instance.GetTextColor()
            };

            trackAnimationSpeed = new TrackBar
            {
                Location = new Point(310, 45),
                Size = new Size(200, 35),
                Minimum = 100,
                Maximum = 1000,
                TickFrequency = 100,
                Value = 300
            };

            btnPreview = new ModernButton
            {
                Text = "معاينة",
                Location = new Point(550, 50),
                Size = new Size(80, 30),
                BackColor = ThemeManager.Instance.GetPrimaryColor(),
                ForeColor = Color.White,
                BorderRadius = 6
            };
            btnPreview.Click += BtnPreview_Click;

            animationPanel.Controls.AddRange(new Control[] { 
                lblAnimationTitle, chkEnableAnimations, lblAnimationSpeed, trackAnimationSpeed, btnPreview 
            });
            mainPanel.Controls.Add(animationPanel);
        }

        private void CreateFontSection()
        {
            fontPanel = new ModernPanel
            {
                Size = new Size(740, 120),
                Location = new Point(20, 400),
                BackColor = ThemeManager.Instance.GetSurfaceColor(),
                BorderRadius = 8
            };

            Label lblFontTitle = new Label
            {
                Text = "إعدادات الخطوط",
                Location = new Point(15, 15),
                Size = new Size(200, 25),
                Font = new Font("Segoe UI", 12, FontStyle.Bold),
                ForeColor = ThemeManager.Instance.GetTextColor()
            };

            Label lblFontFamily = new Label
            {
                Text = "نوع الخط:",
                Location = new Point(15, 50),
                Size = new Size(80, 25),
                Font = new Font("Segoe UI", 10),
                ForeColor = ThemeManager.Instance.GetTextColor()
            };

            cmbFontFamily = new ComboBox
            {
                Location = new Point(100, 50),
                Size = new Size(150, 25),
                DropDownStyle = ComboBoxStyle.DropDownList
            };
            cmbFontFamily.Items.AddRange(new[] { "Segoe UI", "Arial", "Tahoma", "Calibri", "Times New Roman" });

            Label lblFontSize = new Label
            {
                Text = "حجم الخط:",
                Location = new Point(300, 50),
                Size = new Size(80, 25),
                Font = new Font("Segoe UI", 10),
                ForeColor = ThemeManager.Instance.GetTextColor()
            };

            trackFontSize = new TrackBar
            {
                Location = new Point(390, 45),
                Size = new Size(150, 35),
                Minimum = 8,
                Maximum = 16,
                TickFrequency = 2,
                Value = 10
            };

            chkBoldHeaders = new CheckBox
            {
                Text = "عناوين عريضة",
                Location = new Point(580, 50),
                Size = new Size(120, 25),
                Font = new Font("Segoe UI", 10),
                ForeColor = ThemeManager.Instance.GetTextColor()
            };

            fontPanel.Controls.AddRange(new Control[] { 
                lblFontTitle, lblFontFamily, cmbFontFamily, lblFontSize, trackFontSize, chkBoldHeaders 
            });
            mainPanel.Controls.Add(fontPanel);
        }

        private void CreateActionButtons()
        {
            btnSave = new ModernButton
            {
                Text = "حفظ",
                Location = new Point(20, 540),
                Size = new Size(100, 40),
                BackColor = Color.FromArgb(46, 204, 113),
                ForeColor = Color.White,
                BorderRadius = 8
            };
            btnSave.Click += BtnSave_Click;

            btnCancel = new ModernButton
            {
                Text = "إلغاء",
                Location = new Point(140, 540),
                Size = new Size(100, 40),
                BackColor = Color.FromArgb(231, 76, 60),
                ForeColor = Color.White,
                BorderRadius = 8
            };
            btnCancel.Click += BtnCancel_Click;

            btnReset = new ModernButton
            {
                Text = "إعادة تعيين",
                Location = new Point(260, 540),
                Size = new Size(120, 40),
                BackColor = Color.FromArgb(155, 89, 182),
                ForeColor = Color.White,
                BorderRadius = 8
            };
            btnReset.Click += BtnReset_Click;

            mainPanel.Controls.AddRange(new Control[] { btnSave, btnCancel, btnReset });
        }

        private void LoadCurrentSettings()
        {
            var settings = UISettings.Instance;

            // تحميل إعدادات الثيم
            cmbTheme.SelectedIndex = (int)settings.CurrentTheme;
            chkEnableShadows.Checked = settings.EnableShadows;
            trackBorderRadius.Value = settings.BorderRadius;

            // تحميل إعدادات اللغة
            cmbLanguage.SelectedIndex = settings.Language == "ar" ? 0 : 1;

            // تحميل إعدادات الحركة
            chkEnableAnimations.Checked = settings.EnableAnimations;
            trackAnimationSpeed.Value = settings.AnimationSpeed;

            // تحميل إعدادات الخطوط
            cmbFontFamily.Text = settings.FontFamily;
            trackFontSize.Value = (int)settings.FontSize;
            chkBoldHeaders.Checked = settings.UseBoldHeaders;
        }

        private void LoadLanguage()
        {
            var lang = LanguageManager.Instance;
            this.Text = lang.GetText("UISettings");
        }

        private void ApplyTheme()
        {
            var theme = ThemeManager.Instance;
            this.BackColor = theme.GetBackgroundColor();
        }

        private void CmbTheme_SelectedIndexChanged(object sender, EventArgs e)
        {
            ThemeManager.Instance.CurrentTheme = (ThemeType)cmbTheme.SelectedIndex;
            ApplyTheme();
        }

        private void CmbLanguage_SelectedIndexChanged(object sender, EventArgs e)
        {
            string language = cmbLanguage.SelectedIndex == 0 ? "ar" : "fr";
            LanguageManager.Instance.CurrentLanguage = language;
            LoadLanguage();
        }

        private void ChkEnableAnimations_CheckedChanged(object sender, EventArgs e)
        {
            trackAnimationSpeed.Enabled = chkEnableAnimations.Checked;
            btnPreview.Enabled = chkEnableAnimations.Checked;
        }

        private void BtnPreview_Click(object sender, EventArgs e)
        {
            // معاينة الحركات
            AnimationManager.Pulse(btnPreview, 2, trackAnimationSpeed.Value / 3);
        }

        private void BtnSave_Click(object sender, EventArgs e)
        {
            var settings = UISettings.Instance;

            // حفظ إعدادات الثيم
            settings.CurrentTheme = (ThemeType)cmbTheme.SelectedIndex;
            settings.EnableShadows = chkEnableShadows.Checked;
            settings.BorderRadius = trackBorderRadius.Value;

            // حفظ إعدادات اللغة
            settings.Language = cmbLanguage.SelectedIndex == 0 ? "ar" : "fr";

            // حفظ إعدادات الحركة
            settings.EnableAnimations = chkEnableAnimations.Checked;
            settings.AnimationSpeed = trackAnimationSpeed.Value;

            // حفظ إعدادات الخطوط
            settings.FontFamily = cmbFontFamily.Text;
            settings.FontSize = trackFontSize.Value;
            settings.UseBoldHeaders = chkBoldHeaders.Checked;

            settings.SaveSettings();

            MessageBox.Show("تم حفظ الإعدادات بنجاح", "نجح", MessageBoxButtons.OK, MessageBoxIcon.Information);
            this.DialogResult = DialogResult.OK;
            this.Close();
        }

        private void BtnCancel_Click(object sender, EventArgs e)
        {
            this.DialogResult = DialogResult.Cancel;
            this.Close();
        }

        private void BtnReset_Click(object sender, EventArgs e)
        {
            var result = MessageBox.Show("هل تريد إعادة تعيين جميع الإعدادات للقيم الافتراضية؟", 
                                        "تأكيد", MessageBoxButtons.YesNo, MessageBoxIcon.Question);
            if (result == DialogResult.Yes)
            {
                UISettings.Instance.ResetToDefaults();
                LoadCurrentSettings();
                ApplyTheme();
            }
        }
    }
}
