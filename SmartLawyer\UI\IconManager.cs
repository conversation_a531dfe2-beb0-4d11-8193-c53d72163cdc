using System;
using System.Collections.Generic;
using System.Drawing;
using System.Drawing.Drawing2D;
using System.Drawing.Text;
using System.Windows.Forms;

namespace SmartLawyer.UI
{
    public static class IconManager
    {
        // أيقونات قانونية احترافية مرسومة بـ SVG
        private static readonly Dictionary<string, string> LegalIcons = new Dictionary<string, string>
        {
            // الأيقونات الرئيسية
            ["clients"] = "M12 12c2.21 0 4-1.79 4-4s-1.79-4-4-4-4 1.79-4 4 1.79 4 4 4zm0 2c-2.67 0-8 1.34-8 4v2h16v-2c0-2.66-5.33-4-8-4z",
            ["cases"] = "M14 2H6c-1.1 0-1.99.9-1.99 2L4 20c0 1.1.89 2 2 2h12c1.1 0 2-.9 2-2V8l-6-6zm4 18H6V4h7v5h5v11z",
            ["hearings"] = "M12 2C6.48 2 2 6.48 2 12s4.48 10 10 10 10-4.48 10-10S17.52 2 12 2zm-2 15l-5-5 1.41-1.41L10 14.17l7.59-7.59L19 8l-9 9z",
            ["appointments"] = "M19 3h-1V1h-2v2H8V1H6v2H5c-1.11 0-1.99.9-1.99 2L3 19c0 1.1.89 2 2 2h14c1.1 0 2-.9 2-2V5c0-1.1-.9-2-2-2zm0 16H5V8h14v11zM7 10h5v5H7z",
            ["procedures"] = "M14 2H6c-1.1 0-2 .9-2 2v16c0 1.1.89 2 2 2h12c1.1 0 2-.9 2-2V8l-6-6zM16 18H8v-2h8v2zm0-4H8v-2h8v2zm-3-5V3.5L18.5 9H13z",
            ["expenses"] = "M11.8 10.9c-2.27-.59-3-1.2-3-2.15 0-1.09 1.01-1.85 2.7-1.85 1.78 0 2.44.85 2.5 2.1h2.21c-.07-1.72-1.12-3.3-3.21-3.81V3h-3v2.16c-1.94.42-3.5 1.68-3.5 3.61 0 2.31 1.91 3.46 4.7 4.13 2.5.6 3 1.48 3 2.41 0 .69-.49 1.79-2.7 1.79-2.06 0-2.87-.92-2.98-2.1h-2.2c.12 2.19 1.76 3.42 3.68 3.83V21h3v-2.15c1.95-.37 3.5-1.5 3.5-3.55 0-2.84-2.43-3.81-4.7-4.4z",
            ["users"] = "M16 4c0-1.11.89-2 2-2s2 .89 2 2-.89 2-2 2-2-.89-2-2zM4 18v-4c0-2 4-3 6-3s6 1 6 3v4H4zM10 4c0-1.11.89-2 2-2s2 .89 2 2-.89 2-2 2-2-.89-2-2z",
            ["notifications"] = "M12 22c1.1 0 2-.9 2-2h-4c0 1.1.89 2 2 2zm6-6v-5c0-3.07-1.64-5.64-4.5-6.32V4c0-.83-.67-1.5-1.5-1.5s-1.5.67-1.5 1.5v.68C7.63 5.36 6 7.92 6 11v5l-2 2v1h16v-1l-2-2z",
            ["reports"] = "M19 3H5c-1.1 0-2 .9-2 2v14c0 1.1.9 2 2 2h14c1.1 0 2-.9 2-2V5c0-1.1-.9-2-2-2zM9 17H7v-7h2v7zm4 0h-2V7h2v10zm4 0h-2v-4h2v4z",
            ["settings"] = "M19.14,12.94c0.04-0.3,0.06-0.61,0.06-0.94c0-0.32-0.02-0.64-0.07-0.94l2.03-1.58c0.18-0.14,0.23-0.41,0.12-0.61 l-1.92-3.32c-0.12-0.22-0.37-0.29-0.59-0.22l-2.39,0.96c-0.5-0.38-1.03-0.7-1.62-0.94L14.4,2.81c-0.04-0.24-0.24-0.41-0.48-0.41 h-3.84c-0.24,0-0.44,0.17-0.47,0.41L9.25,5.35C8.66,5.59,8.12,5.92,7.63,6.29L5.24,5.33c-0.22-0.08-0.47,0-0.59,0.22L2.74,8.87 C2.62,9.08,2.66,9.34,2.86,9.48l2.03,1.58C4.84,11.36,4.8,11.69,4.8,12s0.02,0.64,0.07,0.94l-2.03,1.58 c-0.18,0.14-0.23,0.41-0.12,0.61l1.92,3.32c0.12,0.22,0.37,0.29,0.59,0.22l2.39-0.96c0.5,0.38,1.03,0.7,1.62,0.94l0.36,2.54 c0.05,0.24,0.24,0.41,0.48,0.41h3.84c0.24,0,0.44-0.17,0.47-0.41l0.36-2.54c0.59-0.24,1.13-0.56,1.62-0.94l2.39,0.96 c0.22,0.08,0.47,0,0.59-0.22l1.92-3.32c0.12-0.22,0.07-0.47-0.12-0.61L19.14,12.94z M12,15.6c-1.98,0-3.6-1.62-3.6-3.6 s1.62-3.6,3.6-3.6s3.6,1.62,3.6,3.6S13.98,15.6,12,15.6z",
            ["logout"] = "M17 7l-1.41 1.41L18.17 11H8v2h10.17l-2.58 2.59L17 17l5-5zM4 5h8V3H4c-1.1 0-2 .9-2 2v14c0 1.1.9 2 2 2h8v-2H4V5z",
            
            // أيقونات إضافية
            ["law"] = "M12 2l3.09 6.26L22 9.27l-5 4.87 1.18 6.88L12 17.77l-6.18 3.25L7 14.14 2 9.27l6.91-1.01L12 2z",
            ["justice"] = "M12 2C6.48 2 2 6.48 2 12s4.48 10 10 10 10-4.48 10-10S17.52 2 12 2zm-2 15l-5-5 1.41-1.41L10 14.17l7.59-7.59L19 8l-9 9z",
            ["contract"] = "M14 2H6c-1.1 0-1.99.9-1.99 2L4 20c0 1.1.89 2 2 2h12c1.1 0 2-.9 2-2V8l-6-6zm2 16H8v-2h8v2zm0-4H8v-2h8v2zm-3-5V3.5L18.5 9H13z",
            ["court"] = "M12 2l3.09 6.26L22 9.27l-5 4.87 1.18 6.88L12 17.77l-6.18 3.25L7 14.14 2 9.27l6.91-1.01L12 2z"
        };

        // رسم أيقونة SVG
        public static Bitmap CreateIcon(string iconName, int size, Color color)
        {
            if (!LegalIcons.ContainsKey(iconName))
                return CreateDefaultIcon(size, color);

            var bitmap = new Bitmap(size, size);
            using (var graphics = Graphics.FromImage(bitmap))
            {
                graphics.SmoothingMode = SmoothingMode.AntiAlias;
                graphics.TextRenderingHint = TextRenderingHint.AntiAlias;
                graphics.Clear(Color.Transparent);

                // رسم الأيقونة
                using (var brush = new SolidBrush(color))
                {
                    DrawSVGPath(graphics, LegalIcons[iconName], brush, size);
                }
            }
            return bitmap;
        }

        // رسم أيقونة مع خلفية دائرية
        public static Bitmap CreateCircularIcon(string iconName, int size, Color iconColor, Color backgroundColor)
        {
            var bitmap = new Bitmap(size, size);
            using (var graphics = Graphics.FromImage(bitmap))
            {
                graphics.SmoothingMode = SmoothingMode.AntiAlias;
                graphics.Clear(Color.Transparent);

                // رسم الخلفية الدائرية
                using (var backgroundBrush = new SolidBrush(backgroundColor))
                {
                    graphics.FillEllipse(backgroundBrush, 0, 0, size, size);
                }

                // رسم الأيقونة في المنتصف
                int iconSize = (int)(size * 0.6);
                int offset = (size - iconSize) / 2;
                
                if (LegalIcons.ContainsKey(iconName))
                {
                    using (var iconBrush = new SolidBrush(iconColor))
                    {
                        var iconGraphics = graphics;
                        iconGraphics.TranslateTransform(offset, offset);
                        DrawSVGPath(iconGraphics, LegalIcons[iconName], iconBrush, iconSize);
                    }
                }
            }
            return bitmap;
        }

        // رسم أيقونة مع تدرج
        public static Bitmap CreateGradientIcon(string iconName, int size, Color startColor, Color endColor)
        {
            var bitmap = new Bitmap(size, size);
            using (var graphics = Graphics.FromImage(bitmap))
            {
                graphics.SmoothingMode = SmoothingMode.AntiAlias;
                graphics.Clear(Color.Transparent);

                if (LegalIcons.ContainsKey(iconName))
                {
                    using (var gradientBrush = new LinearGradientBrush(
                        new Rectangle(0, 0, size, size), 
                        startColor, 
                        endColor, 
                        LinearGradientMode.Vertical))
                    {
                        DrawSVGPath(graphics, LegalIcons[iconName], gradientBrush, size);
                    }
                }
            }
            return bitmap;
        }

        // رسم أيقونة افتراضية
        private static Bitmap CreateDefaultIcon(int size, Color color)
        {
            var bitmap = new Bitmap(size, size);
            using (var graphics = Graphics.FromImage(bitmap))
            {
                graphics.SmoothingMode = SmoothingMode.AntiAlias;
                graphics.Clear(Color.Transparent);

                using (var brush = new SolidBrush(color))
                {
                    graphics.FillEllipse(brush, size / 4, size / 4, size / 2, size / 2);
                }
            }
            return bitmap;
        }

        // رسم مسار SVG مبسط
        private static void DrawSVGPath(Graphics graphics, string pathData, Brush brush, int size)
        {
            try
            {
                // تحويل مبسط لمسارات SVG
                // هذا مثال مبسط - في التطبيق الحقيقي نحتاج مكتبة SVG كاملة
                using (var path = new GraphicsPath())
                {
                    // رسم شكل أساسي بناءً على نوع الأيقونة
                    var rect = new Rectangle(2, 2, size - 4, size - 4);
                    
                    if (pathData.Contains("circle") || pathData.Contains("12 12c2.21"))
                    {
                        // أيقونة دائرية (مثل المستخدمين)
                        path.AddEllipse(rect);
                    }
                    else if (pathData.Contains("M14 2H6") || pathData.Contains("document"))
                    {
                        // أيقونة مستند
                        var docRect = new Rectangle(rect.X, rect.Y, rect.Width - 6, rect.Height);
                        path.AddRectangle(docRect);
                        var cornerPoints = new Point[]
                        {
                            new Point(docRect.Right, docRect.Y),
                            new Point(docRect.Right + 6, docRect.Y + 6),
                            new Point(docRect.Right, docRect.Y + 6)
                        };
                        path.AddPolygon(cornerPoints);
                    }
                    else if (pathData.Contains("M19 3H5") || pathData.Contains("calendar"))
                    {
                        // أيقونة تقويم أو تقرير
                        path.AddRectangle(rect);
                        // إضافة خطوط داخلية
                        for (int i = 1; i < 4; i++)
                        {
                            int y = rect.Y + (rect.Height * i / 4);
                            path.AddRectangle(new Rectangle(rect.X + 4, y, rect.Width - 8, 2));
                        }
                    }
                    else
                    {
                        // شكل افتراضي
                        path.AddRectangle(rect);
                    }

                    graphics.FillPath(brush, path);
                }
            }
            catch
            {
                // في حالة الخطأ، ارسم مربع بسيط
                graphics.FillRectangle(brush, 2, 2, size - 4, size - 4);
            }
        }

        // إنشاء أيقونة للأزرار
        public static void SetButtonIcon(Button button, string iconName, int size = 24)
        {
            var theme = ThemeManager.Instance;
            var iconColor = button.ForeColor;
            
            if (button.BackColor == theme.GetPrimaryColor())
            {
                iconColor = theme.GetTextOnPrimaryColor();
            }

            var icon = CreateIcon(iconName, size, iconColor);
            button.Image = icon;
            button.ImageAlign = ContentAlignment.MiddleLeft;
            button.TextImageRelation = TextImageRelation.ImageBeforeText;
        }

        // إنشاء أيقونة للقوائم
        public static void SetMenuIcon(ToolStripMenuItem menuItem, string iconName, int size = 16)
        {
            var theme = ThemeManager.Instance;
            var icon = CreateIcon(iconName, size, theme.GetTextColor());
            menuItem.Image = icon;
        }

        // الحصول على أيقونة حسب الحالة
        public static Bitmap GetStatusIcon(string status, int size = 16)
        {
            var theme = ThemeManager.Instance;
            Color color = status.ToLower() switch
            {
                "success" or "completed" or "active" => theme.GetSuccessColor(),
                "warning" or "pending" or "in-progress" => theme.GetWarningColor(),
                "error" or "failed" or "cancelled" => theme.GetErrorColor(),
                "info" or "new" => theme.GetInfoColor(),
                _ => theme.GetTextSecondaryColor()
            };

            return CreateCircularIcon("justice", size, Color.White, color);
        }
    }
}
