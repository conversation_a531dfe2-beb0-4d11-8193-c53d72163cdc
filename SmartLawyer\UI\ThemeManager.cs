using System;
using System.Drawing;
using System.Drawing.Drawing2D;
using System.Windows.Forms;

namespace SmartLawyer.UI
{
    public enum ThemeType
    {
        LegalPlatinum,   // الثيم البلاتيني الراقي (أبيض، رمادي فاتح، ذهبي)
        LegalRoyal,      // الثيم الملكي (أزرق داكن، ذهبي، أبيض)
        LegalClassic,    // الثيم الكلاسيكي (كريمي، بني داكن، ذهبي)
        LegalModern      // الثيم العصري (أبيض، رمادي، أزرق فاتح)
    }

    public class ThemeManager
    {
        private static ThemeManager _instance;
        private static readonly object _lock = new object();
        private ThemeType _currentTheme = ThemeType.LegalPlatinum;

        private ThemeManager() { }

        public static ThemeManager Instance
        {
            get
            {
                if (_instance == null)
                {
                    lock (_lock)
                    {
                        if (_instance == null)
                            _instance = new ThemeManager();
                    }
                }
                return _instance;
            }
        }

        public ThemeType CurrentTheme
        {
            get { return _currentTheme; }
            set
            {
                _currentTheme = value;
                OnThemeChanged?.Invoke(value);
            }
        }

        public event Action<ThemeType> OnThemeChanged;

        // الثيم البلاتيني الراقي (أبيض، رمادي فاتح، ذهبي)
        public static class LegalPlatinumTheme
        {
            public static readonly Color Primary = Color.FromArgb(212, 175, 55);      // ذهبي أنيق
            public static readonly Color PrimaryDark = Color.FromArgb(184, 134, 11);  // ذهبي داكن
            public static readonly Color Secondary = Color.FromArgb(71, 85, 105);     // رمادي أزرق راقي
            public static readonly Color Accent = Color.FromArgb(245, 158, 11);       // ذهبي لامع
            public static readonly Color Success = Color.FromArgb(34, 197, 94);       // أخضر
            public static readonly Color Warning = Color.FromArgb(245, 158, 11);      // ذهبي
            public static readonly Color Danger = Color.FromArgb(220, 38, 38);        // أحمر راقي
            public static readonly Color Info = Color.FromArgb(59, 130, 246);         // أزرق

            public static readonly Color Background = Color.FromArgb(248, 250, 252);  // أبيض بلاتيني
            public static readonly Color Surface = Color.White;
            public static readonly Color SurfaceVariant = Color.FromArgb(241, 245, 249);
            public static readonly Color SurfaceElevated = Color.FromArgb(255, 255, 255);

            public static readonly Color TextPrimary = Color.FromArgb(15, 23, 42);    // أسود راقي
            public static readonly Color TextSecondary = Color.FromArgb(51, 65, 85);  // رمادي داكن
            public static readonly Color TextMuted = Color.FromArgb(100, 116, 139);   // رمادي متوسط
            public static readonly Color TextOnPrimary = Color.White;

            public static readonly Color Border = Color.FromArgb(226, 232, 240);      // رمادي فاتح راقي
            public static readonly Color BorderLight = Color.FromArgb(241, 245, 249);
            public static readonly Color BorderDark = Color.FromArgb(203, 213, 225);

            public static readonly Color Shadow = Color.FromArgb(40, 0, 0, 0);
            public static readonly Color ShadowLight = Color.FromArgb(20, 0, 0, 0);
            public static readonly Color ShadowGold = Color.FromArgb(30, 212, 175, 55);
        }

        // الثيم الملكي (أزرق داكن، ذهبي، أبيض)
        public static class LegalRoyalTheme
        {
            public static readonly Color Primary = Color.FromArgb(30, 58, 138);       // أزرق ملكي
            public static readonly Color PrimaryDark = Color.FromArgb(23, 37, 84);    // أزرق داكن
            public static readonly Color Secondary = Color.FromArgb(212, 175, 55);    // ذهبي ملكي
            public static readonly Color Accent = Color.FromArgb(245, 158, 11);       // ذهبي لامع
            public static readonly Color Success = Color.FromArgb(34, 197, 94);
            public static readonly Color Warning = Color.FromArgb(245, 158, 11);
            public static readonly Color Danger = Color.FromArgb(220, 38, 38);
            public static readonly Color Info = Color.FromArgb(59, 130, 246);

            public static readonly Color Background = Color.FromArgb(248, 250, 252);  // أبيض ملكي
            public static readonly Color Surface = Color.White;
            public static readonly Color SurfaceVariant = Color.FromArgb(241, 245, 249);
            public static readonly Color SurfaceElevated = Color.FromArgb(255, 255, 255);

            public static readonly Color TextPrimary = Color.FromArgb(15, 23, 42);
            public static readonly Color TextSecondary = Color.FromArgb(51, 65, 85);
            public static readonly Color TextMuted = Color.FromArgb(100, 116, 139);
            public static readonly Color TextOnPrimary = Color.White;

            public static readonly Color Border = Color.FromArgb(226, 232, 240);
            public static readonly Color BorderLight = Color.FromArgb(241, 245, 249);
            public static readonly Color BorderDark = Color.FromArgb(203, 213, 225);

            public static readonly Color Shadow = Color.FromArgb(40, 30, 58, 138);
            public static readonly Color ShadowLight = Color.FromArgb(20, 30, 58, 138);
            public static readonly Color ShadowBlue = Color.FromArgb(30, 30, 58, 138);
        }

        // الثيم الكلاسيكي (كريمي، بني داكن، ذهبي)
        public static class LegalClassicTheme
        {
            public static readonly Color Primary = Color.FromArgb(133, 77, 14);       // بني ذهبي كلاسيكي
            public static readonly Color PrimaryDark = Color.FromArgb(92, 51, 23);    // بني داكن
            public static readonly Color Secondary = Color.FromArgb(212, 175, 55);    // ذهبي كلاسيكي
            public static readonly Color Accent = Color.FromArgb(245, 158, 11);       // ذهبي لامع
            public static readonly Color Success = Color.FromArgb(34, 197, 94);
            public static readonly Color Warning = Color.FromArgb(245, 158, 11);
            public static readonly Color Danger = Color.FromArgb(220, 38, 38);
            public static readonly Color Info = Color.FromArgb(59, 130, 246);

            public static readonly Color Background = Color.FromArgb(254, 252, 232);  // كريمي دافئ
            public static readonly Color Surface = Color.FromArgb(255, 255, 255);
            public static readonly Color SurfaceVariant = Color.FromArgb(250, 245, 255);
            public static readonly Color SurfaceElevated = Color.FromArgb(255, 255, 255);

            public static readonly Color TextPrimary = Color.FromArgb(92, 51, 23);
            public static readonly Color TextSecondary = Color.FromArgb(133, 77, 14);
            public static readonly Color TextMuted = Color.FromArgb(156, 163, 175);
            public static readonly Color TextOnPrimary = Color.White;

            public static readonly Color Border = Color.FromArgb(229, 231, 235);
            public static readonly Color BorderLight = Color.FromArgb(243, 244, 246);
            public static readonly Color BorderDark = Color.FromArgb(209, 213, 219);

            public static readonly Color Shadow = Color.FromArgb(40, 133, 77, 14);
            public static readonly Color ShadowLight = Color.FromArgb(20, 133, 77, 14);
            public static readonly Color ShadowGold = Color.FromArgb(30, 212, 175, 55);
        }

        // الثيم العصري (أبيض، رمادي، أزرق فاتح)
        public static class LegalModernTheme
        {
            public static readonly Color Primary = Color.FromArgb(59, 130, 246);      // أزرق عصري
            public static readonly Color PrimaryDark = Color.FromArgb(37, 99, 235);   // أزرق داكن
            public static readonly Color Secondary = Color.FromArgb(107, 114, 128);   // رمادي أنيق
            public static readonly Color Accent = Color.FromArgb(16, 185, 129);       // أخضر عصري
            public static readonly Color Success = Color.FromArgb(34, 197, 94);
            public static readonly Color Warning = Color.FromArgb(245, 158, 11);
            public static readonly Color Danger = Color.FromArgb(239, 68, 68);
            public static readonly Color Info = Color.FromArgb(59, 130, 246);

            public static readonly Color Background = Color.FromArgb(249, 250, 251);  // رمادي فاتح جداً
            public static readonly Color Surface = Color.FromArgb(255, 255, 255);
            public static readonly Color SurfaceVariant = Color.FromArgb(243, 244, 246);
            public static readonly Color SurfaceElevated = Color.FromArgb(255, 255, 255);

            public static readonly Color TextPrimary = Color.FromArgb(17, 24, 39);
            public static readonly Color TextSecondary = Color.FromArgb(75, 85, 99);
            public static readonly Color TextMuted = Color.FromArgb(156, 163, 175);
            public static readonly Color TextOnPrimary = Color.White;

            public static readonly Color Border = Color.FromArgb(229, 231, 235);
            public static readonly Color BorderLight = Color.FromArgb(243, 244, 246);
            public static readonly Color BorderDark = Color.FromArgb(209, 213, 219);

            public static readonly Color Shadow = Color.FromArgb(30, 59, 130, 246);
            public static readonly Color ShadowLight = Color.FromArgb(15, 59, 130, 246);
            public static readonly Color ShadowBlue = Color.FromArgb(40, 59, 130, 246);
        }

        public Color GetPrimaryColor()
        {
            return _currentTheme switch
            {
                ThemeType.LegalPlatinum => LegalPlatinumTheme.Primary,
                ThemeType.LegalRoyal => LegalRoyalTheme.Primary,
                ThemeType.LegalClassic => LegalClassicTheme.Primary,
                ThemeType.LegalModern => LegalModernTheme.Primary,
                _ => LegalPlatinumTheme.Primary
            };
        }

        public Color GetSecondaryColor()
        {
            return _currentTheme switch
            {
                ThemeType.LegalPlatinum => LegalPlatinumTheme.Secondary,
                ThemeType.LegalRoyal => LegalRoyalTheme.Secondary,
                ThemeType.LegalClassic => LegalClassicTheme.Secondary,
                ThemeType.LegalModern => LegalModernTheme.Secondary,
                _ => LegalPlatinumTheme.Secondary
            };
        }

        public Color GetAccentColor()
        {
            return _currentTheme switch
            {
                ThemeType.LegalPlatinum => LegalPlatinumTheme.Accent,
                ThemeType.LegalRoyal => LegalRoyalTheme.Accent,
                ThemeType.LegalClassic => LegalClassicTheme.Accent,
                ThemeType.LegalModern => LegalModernTheme.Accent,
                _ => LegalPlatinumTheme.Accent
            };
        }

        public Color GetBackgroundColor()
        {
            return _currentTheme switch
            {
                ThemeType.LegalPlatinum => LegalPlatinumTheme.Background,
                ThemeType.LegalRoyal => LegalRoyalTheme.Background,
                ThemeType.LegalClassic => LegalClassicTheme.Background,
                ThemeType.LegalModern => LegalModernTheme.Background,
                _ => LegalPlatinumTheme.Background
            };
        }

        public Color GetSurfaceColor()
        {
            return _currentTheme switch
            {
                ThemeType.LegalPlatinum => LegalPlatinumTheme.Surface,
                ThemeType.LegalRoyal => LegalRoyalTheme.Surface,
                ThemeType.LegalClassic => LegalClassicTheme.Surface,
                ThemeType.LegalModern => LegalModernTheme.Surface,
                _ => LegalPlatinumTheme.Surface
            };
        }

        public Color GetSurfaceElevatedColor()
        {
            return _currentTheme switch
            {
                ThemeType.LegalPlatinum => LegalPlatinumTheme.SurfaceElevated,
                ThemeType.LegalRoyal => LegalRoyalTheme.SurfaceElevated,
                ThemeType.LegalClassic => LegalClassicTheme.SurfaceElevated,
                ThemeType.LegalModern => LegalModernTheme.SurfaceElevated,
                _ => LegalPlatinumTheme.SurfaceElevated
            };
        }

        public Color GetTextColor()
        {
            return _currentTheme switch
            {
                ThemeType.LegalPlatinum => LegalPlatinumTheme.TextPrimary,
                ThemeType.LegalRoyal => LegalRoyalTheme.TextPrimary,
                ThemeType.LegalClassic => LegalClassicTheme.TextPrimary,
                ThemeType.LegalModern => LegalModernTheme.TextPrimary,
                _ => LegalPlatinumTheme.TextPrimary
            };
        }

        public Color GetTextSecondaryColor()
        {
            return _currentTheme switch
            {
                ThemeType.LegalPlatinum => LegalPlatinumTheme.TextSecondary,
                ThemeType.LegalRoyal => LegalRoyalTheme.TextSecondary,
                ThemeType.LegalClassic => LegalClassicTheme.TextSecondary,
                ThemeType.LegalModern => LegalModernTheme.TextSecondary,
                _ => LegalPlatinumTheme.TextSecondary
            };
        }

        public Color GetTextOnPrimaryColor()
        {
            return _currentTheme switch
            {
                ThemeType.LegalPlatinum => LegalPlatinumTheme.TextOnPrimary,
                ThemeType.LegalRoyal => LegalRoyalTheme.TextOnPrimary,
                ThemeType.LegalClassic => LegalClassicTheme.TextOnPrimary,
                ThemeType.LegalModern => LegalModernTheme.TextOnPrimary,
                _ => LegalPlatinumTheme.TextOnPrimary
            };
        }



        public Color GetBorderColor()
        {
            return _currentTheme switch
            {
                ThemeType.LegalPlatinum => LegalPlatinumTheme.Border,
                ThemeType.LegalRoyal => LegalRoyalTheme.Border,
                ThemeType.LegalClassic => LegalClassicTheme.Border,
                ThemeType.LegalModern => LegalModernTheme.Border,
                _ => LegalPlatinumTheme.Border
            };
        }

        public Color GetSuccessColor()
        {
            return _currentTheme switch
            {
                ThemeType.LegalPlatinum => LegalPlatinumTheme.Success,
                ThemeType.LegalRoyal => LegalRoyalTheme.Success,
                ThemeType.LegalClassic => LegalClassicTheme.Success,
                ThemeType.LegalModern => LegalModernTheme.Success,
                _ => LegalPlatinumTheme.Success
            };
        }

        public Color GetWarningColor()
        {
            return _currentTheme switch
            {
                ThemeType.LegalPlatinum => LegalPlatinumTheme.Warning,
                ThemeType.LegalRoyal => LegalRoyalTheme.Warning,
                ThemeType.LegalClassic => LegalClassicTheme.Warning,
                ThemeType.LegalModern => LegalModernTheme.Warning,
                _ => LegalPlatinumTheme.Warning
            };
        }

        public Color GetErrorColor()
        {
            return _currentTheme switch
            {
                ThemeType.LegalClassic => LegalClassicTheme.Danger,
                ThemeType.LegalModern => LegalModernTheme.Danger,
                ThemeType.LegalDark => LegalDarkTheme.Danger,
                ThemeType.LegalBlue => LegalBlueTheme.Danger,
                _ => LegalClassicTheme.Danger
            };
        }

        public Color GetInfoColor()
        {
            return _currentTheme switch
            {
                ThemeType.LegalClassic => LegalClassicTheme.Info,
                ThemeType.LegalModern => LegalModernTheme.Info,
                ThemeType.LegalDark => LegalDarkTheme.Info,
                ThemeType.LegalBlue => LegalBlueTheme.Info,
                _ => LegalClassicTheme.Info
            };
        }

        public Color GetShadowColor()
        {
            return _currentTheme switch
            {
                ThemeType.LegalClassic => LegalClassicTheme.Shadow,
                ThemeType.LegalModern => LegalModernTheme.Shadow,
                ThemeType.LegalDark => LegalDarkTheme.Shadow,
                ThemeType.LegalBlue => LegalBlueTheme.Shadow,
                _ => LegalClassicTheme.Shadow
            };
        }
    }

    public static class UIHelper
    {
        public static void ApplyRoundedCorners(Control control, int radius = 10)
        {
            GraphicsPath path = new GraphicsPath();
            Rectangle rect = new Rectangle(0, 0, control.Width, control.Height);
            
            path.AddArc(rect.X, rect.Y, radius, radius, 180, 90);
            path.AddArc(rect.X + rect.Width - radius, rect.Y, radius, radius, 270, 90);
            path.AddArc(rect.X + rect.Width - radius, rect.Y + rect.Height - radius, radius, radius, 0, 90);
            path.AddArc(rect.X, rect.Y + rect.Height - radius, radius, radius, 90, 90);
            path.CloseAllFigures();
            
            control.Region = new Region(path);
        }

        public static void AddShadow(Control control, Color shadowColor, int shadowSize = 5, int shadowOpacity = 50)
        {
            // إنشاء تأثير الظل باستخدام Panel خلفي
            Panel shadowPanel = new Panel
            {
                BackColor = Color.FromArgb(shadowOpacity, shadowColor),
                Size = new Size(control.Width + shadowSize, control.Height + shadowSize),
                Location = new Point(control.Location.X + shadowSize, control.Location.Y + shadowSize)
            };
            
            if (control.Parent != null)
            {
                control.Parent.Controls.Add(shadowPanel);
                shadowPanel.SendToBack();
                ApplyRoundedCorners(shadowPanel, 10);
            }
        }

        public static void AnimateControl(Control control, AnimationType animationType, int duration = 300)
        {
            System.Windows.Forms.Timer animationTimer = new System.Windows.Forms.Timer();
            animationTimer.Interval = 16; // ~60 FPS
            
            DateTime startTime = DateTime.Now;
            Point originalLocation = control.Location;
            Size originalSize = control.Size;
            int originalOpacity = 255;
            
            animationTimer.Tick += (sender, e) =>
            {
                double elapsed = (DateTime.Now - startTime).TotalMilliseconds;
                double progress = Math.Min(elapsed / duration, 1.0);
                
                // تطبيق منحنى الحركة (ease-out)
                progress = 1 - Math.Pow(1 - progress, 3);
                
                switch (animationType)
                {
                    case AnimationType.FadeIn:
                        // تأثير الظهور التدريجي
                        break;
                    case AnimationType.SlideInFromLeft:
                        control.Location = new Point(
                            originalLocation.X - (int)((1 - progress) * 200),
                            originalLocation.Y
                        );
                        break;
                    case AnimationType.SlideInFromRight:
                        control.Location = new Point(
                            originalLocation.X + (int)((1 - progress) * 200),
                            originalLocation.Y
                        );
                        break;
                    case AnimationType.ScaleIn:
                        int newWidth = (int)(originalSize.Width * progress);
                        int newHeight = (int)(originalSize.Height * progress);
                        control.Size = new Size(newWidth, newHeight);
                        control.Location = new Point(
                            originalLocation.X + (originalSize.Width - newWidth) / 2,
                            originalLocation.Y + (originalSize.Height - newHeight) / 2
                        );
                        break;
                }
                
                if (progress >= 1.0)
                {
                    animationTimer.Stop();
                    animationTimer.Dispose();
                }
            };
            
            animationTimer.Start();
        }

        public static Button CreateStyledButton(string text, Color backgroundColor, Color textColor, int width = 120, int height = 40)
        {
            Button button = new Button
            {
                Text = text,
                Size = new Size(width, height),
                BackColor = backgroundColor,
                ForeColor = textColor,
                FlatStyle = FlatStyle.Flat,
                Font = new Font("Segoe UI", 10, FontStyle.Bold),
                Cursor = Cursors.Hand,
                TextAlign = ContentAlignment.MiddleCenter
            };
            
            button.FlatAppearance.BorderSize = 0;
            button.FlatAppearance.MouseOverBackColor = ControlPaint.Light(backgroundColor, 0.1f);
            button.FlatAppearance.MouseDownBackColor = ControlPaint.Dark(backgroundColor, 0.1f);
            
            // إضافة تأثيرات الحركة
            button.MouseEnter += (s, e) =>
            {
                button.BackColor = ControlPaint.Light(backgroundColor, 0.1f);
                AnimateButtonHover(button, true);
            };
            
            button.MouseLeave += (s, e) =>
            {
                button.BackColor = backgroundColor;
                AnimateButtonHover(button, false);
            };
            
            ApplyRoundedCorners(button, 8);
            
            return button;
        }

        private static void AnimateButtonHover(Button button, bool isHover)
        {
            System.Windows.Forms.Timer timer = new System.Windows.Forms.Timer();
            timer.Interval = 16;
            
            DateTime startTime = DateTime.Now;
            Size originalSize = button.Size;
            int targetScale = isHover ? 5 : -5;
            
            timer.Tick += (s, e) =>
            {
                double elapsed = (DateTime.Now - startTime).TotalMilliseconds;
                double progress = Math.Min(elapsed / 150, 1.0);
                
                if (!isHover) progress = 1 - progress;
                
                int scaleAmount = (int)(targetScale * progress);
                button.Size = new Size(
                    originalSize.Width + scaleAmount,
                    originalSize.Height + scaleAmount / 2
                );
                
                if (progress >= 1.0)
                {
                    timer.Stop();
                    timer.Dispose();
                }
            };
            
            timer.Start();
        }

        public static Panel CreateStyledPanel(Color backgroundColor, int borderRadius = 10)
        {
            Panel panel = new Panel
            {
                BackColor = backgroundColor,
                Padding = new Padding(15)
            };
            
            ApplyRoundedCorners(panel, borderRadius);
            return panel;
        }

        public static TextBox CreateStyledTextBox(int width = 200, int height = 35)
        {
            TextBox textBox = new TextBox
            {
                Size = new Size(width, height),
                Font = new Font("Segoe UI", 10),
                BorderStyle = BorderStyle.None,
                BackColor = ThemeManager.Instance.GetSurfaceColor(),
                ForeColor = ThemeManager.Instance.GetTextColor()
            };
            
            // إضافة حدود مخصصة
            Panel borderPanel = new Panel
            {
                Size = new Size(width + 2, height + 2),
                BackColor = ThemeManager.Instance.GetBorderColor()
            };
            
            borderPanel.Controls.Add(textBox);
            textBox.Location = new Point(1, 1);
            
            ApplyRoundedCorners(borderPanel, 6);
            ApplyRoundedCorners(textBox, 5);
            
            return textBox;
        }
    }

    public enum AnimationType
    {
        FadeIn,
        FadeOut,
        SlideInFromLeft,
        SlideInFromRight,
        SlideInFromTop,
        SlideInFromBottom,
        ScaleIn,
        ScaleOut
    }
}
