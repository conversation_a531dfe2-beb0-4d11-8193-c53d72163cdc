using System;
using System.Drawing;
using System.Drawing.Drawing2D;
using System.Windows.Forms;

namespace SmartLawyer.UI
{
    public enum ThemeType
    {
        LegalClassic,    // الثيم القانوني الكلاسيكي (ذهبي وأزرق داكن)
        LegalModern,     // الثيم القانوني العصري (رمادي أنيق وذهبي)
        LegalDark,       // الثيم القانوني الداكن (أسود وذهبي)
        LegalBlue        // الثيم القانوني الأزرق (أزرق ملكي وفضي)
    }

    public class ThemeManager
    {
        private static ThemeManager _instance;
        private static readonly object _lock = new object();
        private ThemeType _currentTheme = ThemeType.Light;

        private ThemeManager() { }

        public static ThemeManager Instance
        {
            get
            {
                if (_instance == null)
                {
                    lock (_lock)
                    {
                        if (_instance == null)
                            _instance = new ThemeManager();
                    }
                }
                return _instance;
            }
        }

        public ThemeType CurrentTheme
        {
            get { return _currentTheme; }
            set
            {
                _currentTheme = value;
                OnThemeChanged?.Invoke(value);
            }
        }

        public event Action<ThemeType> OnThemeChanged;

        // الثيم القانوني الكلاسيكي (ذهبي وأزرق داكن)
        public static class LegalClassicTheme
        {
            public static readonly Color Primary = Color.FromArgb(184, 134, 11);      // ذهبي داكن
            public static readonly Color PrimaryDark = Color.FromArgb(146, 107, 9);   // ذهبي أغمق
            public static readonly Color Secondary = Color.FromArgb(30, 58, 138);     // أزرق داكن
            public static readonly Color Accent = Color.FromArgb(245, 158, 11);       // ذهبي فاتح
            public static readonly Color Success = Color.FromArgb(34, 197, 94);       // أخضر
            public static readonly Color Warning = Color.FromArgb(245, 158, 11);      // ذهبي
            public static readonly Color Danger = Color.FromArgb(239, 68, 68);        // أحمر
            public static readonly Color Info = Color.FromArgb(59, 130, 246);         // أزرق

            public static readonly Color Background = Color.FromArgb(249, 250, 251);  // أبيض دافئ
            public static readonly Color Surface = Color.White;
            public static readonly Color SurfaceVariant = Color.FromArgb(243, 244, 246);
            public static readonly Color SurfaceElevated = Color.FromArgb(255, 255, 255);

            public static readonly Color TextPrimary = Color.FromArgb(31, 41, 55);    // رمادي داكن
            public static readonly Color TextSecondary = Color.FromArgb(75, 85, 99);  // رمادي متوسط
            public static readonly Color TextMuted = Color.FromArgb(156, 163, 175);   // رمادي فاتح
            public static readonly Color TextOnPrimary = Color.White;

            public static readonly Color Border = Color.FromArgb(229, 231, 235);      // رمادي فاتح
            public static readonly Color BorderLight = Color.FromArgb(243, 244, 246);
            public static readonly Color BorderDark = Color.FromArgb(209, 213, 219);

            public static readonly Color Shadow = Color.FromArgb(60, 0, 0, 0);
            public static readonly Color ShadowLight = Color.FromArgb(30, 0, 0, 0);
            public static readonly Color ShadowGold = Color.FromArgb(40, 184, 134, 11);
        }

        // الثيم القانوني العصري (رمادي أنيق وذهبي)
        public static class LegalModernTheme
        {
            public static readonly Color Primary = Color.FromArgb(107, 114, 128);     // رمادي أنيق
            public static readonly Color PrimaryDark = Color.FromArgb(75, 85, 99);    // رمادي داكن
            public static readonly Color Secondary = Color.FromArgb(245, 158, 11);    // ذهبي فاتح
            public static readonly Color Accent = Color.FromArgb(59, 130, 246);       // أزرق عصري
            public static readonly Color Success = Color.FromArgb(34, 197, 94);
            public static readonly Color Warning = Color.FromArgb(245, 158, 11);
            public static readonly Color Danger = Color.FromArgb(239, 68, 68);
            public static readonly Color Info = Color.FromArgb(59, 130, 246);

            public static readonly Color Background = Color.FromArgb(243, 244, 246);  // رمادي فاتح جداً
            public static readonly Color Surface = Color.FromArgb(255, 255, 255);
            public static readonly Color SurfaceVariant = Color.FromArgb(249, 250, 251);
            public static readonly Color SurfaceElevated = Color.FromArgb(255, 255, 255);

            public static readonly Color TextPrimary = Color.FromArgb(55, 65, 81);
            public static readonly Color TextSecondary = Color.FromArgb(107, 114, 128);
            public static readonly Color TextMuted = Color.FromArgb(156, 163, 175);
            public static readonly Color TextOnPrimary = Color.White;

            public static readonly Color Border = Color.FromArgb(209, 213, 219);
            public static readonly Color BorderLight = Color.FromArgb(229, 231, 235);
            public static readonly Color BorderDark = Color.FromArgb(156, 163, 175);

            public static readonly Color Shadow = Color.FromArgb(50, 0, 0, 0);
            public static readonly Color ShadowLight = Color.FromArgb(25, 0, 0, 0);
            public static readonly Color ShadowBlue = Color.FromArgb(30, 59, 130, 246);
        }

        // الثيم القانوني الداكن (أسود وذهبي)
        public static class LegalDarkTheme
        {
            public static readonly Color Primary = Color.FromArgb(217, 119, 6);       // ذهبي برتقالي
            public static readonly Color PrimaryDark = Color.FromArgb(180, 83, 9);    // ذهبي داكن
            public static readonly Color Secondary = Color.FromArgb(156, 163, 175);   // رمادي فاتح
            public static readonly Color Accent = Color.FromArgb(34, 197, 94);        // أخضر نيون
            public static readonly Color Success = Color.FromArgb(34, 197, 94);
            public static readonly Color Warning = Color.FromArgb(245, 158, 11);
            public static readonly Color Danger = Color.FromArgb(248, 113, 113);
            public static readonly Color Info = Color.FromArgb(96, 165, 250);

            public static readonly Color Background = Color.FromArgb(17, 24, 39);     // أسود مزرق
            public static readonly Color Surface = Color.FromArgb(31, 41, 55);        // رمادي داكن
            public static readonly Color SurfaceVariant = Color.FromArgb(55, 65, 81);
            public static readonly Color SurfaceElevated = Color.FromArgb(75, 85, 99);

            public static readonly Color TextPrimary = Color.FromArgb(243, 244, 246);
            public static readonly Color TextSecondary = Color.FromArgb(209, 213, 219);
            public static readonly Color TextMuted = Color.FromArgb(156, 163, 175);
            public static readonly Color TextOnPrimary = Color.FromArgb(17, 24, 39);

            public static readonly Color Border = Color.FromArgb(75, 85, 99);
            public static readonly Color BorderLight = Color.FromArgb(107, 114, 128);
            public static readonly Color BorderDark = Color.FromArgb(55, 65, 81);

            public static readonly Color Shadow = Color.FromArgb(80, 0, 0, 0);
            public static readonly Color ShadowLight = Color.FromArgb(40, 0, 0, 0);
            public static readonly Color ShadowGold = Color.FromArgb(50, 217, 119, 6);
        }

        // الثيم القانوني الأزرق (أزرق ملكي وفضي)
        public static class LegalBlueTheme
        {
            public static readonly Color Primary = Color.FromArgb(30, 58, 138);       // أزرق ملكي
            public static readonly Color PrimaryDark = Color.FromArgb(23, 37, 84);    // أزرق أغمق
            public static readonly Color Secondary = Color.FromArgb(156, 163, 175);   // فضي
            public static readonly Color Accent = Color.FromArgb(245, 158, 11);       // ذهبي
            public static readonly Color Success = Color.FromArgb(34, 197, 94);
            public static readonly Color Warning = Color.FromArgb(245, 158, 11);
            public static readonly Color Danger = Color.FromArgb(239, 68, 68);
            public static readonly Color Info = Color.FromArgb(59, 130, 246);

            public static readonly Color Background = Color.FromArgb(241, 245, 249);  // أزرق فاتح جداً
            public static readonly Color Surface = Color.FromArgb(248, 250, 252);     // أزرق فاتح
            public static readonly Color SurfaceVariant = Color.FromArgb(241, 245, 249);
            public static readonly Color SurfaceElevated = Color.FromArgb(255, 255, 255);

            public static readonly Color TextPrimary = Color.FromArgb(30, 58, 138);
            public static readonly Color TextSecondary = Color.FromArgb(71, 85, 105);
            public static readonly Color TextMuted = Color.FromArgb(148, 163, 184);
            public static readonly Color TextOnPrimary = Color.White;

            public static readonly Color Border = Color.FromArgb(191, 219, 254);
            public static readonly Color BorderLight = Color.FromArgb(219, 234, 254);
            public static readonly Color BorderDark = Color.FromArgb(147, 197, 253);

            public static readonly Color Shadow = Color.FromArgb(40, 30, 58, 138);
            public static readonly Color ShadowLight = Color.FromArgb(20, 30, 58, 138);
            public static readonly Color ShadowBlue = Color.FromArgb(60, 30, 58, 138);
        }

        public Color GetPrimaryColor()
        {
            return _currentTheme switch
            {
                ThemeType.LegalClassic => LegalClassicTheme.Primary,
                ThemeType.LegalModern => LegalModernTheme.Primary,
                ThemeType.LegalDark => LegalDarkTheme.Primary,
                ThemeType.LegalBlue => LegalBlueTheme.Primary,
                _ => LegalClassicTheme.Primary
            };
        }

        public Color GetSecondaryColor()
        {
            return _currentTheme switch
            {
                ThemeType.LegalClassic => LegalClassicTheme.Secondary,
                ThemeType.LegalModern => LegalModernTheme.Secondary,
                ThemeType.LegalDark => LegalDarkTheme.Secondary,
                ThemeType.LegalBlue => LegalBlueTheme.Secondary,
                _ => LegalClassicTheme.Secondary
            };
        }

        public Color GetAccentColor()
        {
            return _currentTheme switch
            {
                ThemeType.LegalClassic => LegalClassicTheme.Accent,
                ThemeType.LegalModern => LegalModernTheme.Accent,
                ThemeType.LegalDark => LegalDarkTheme.Accent,
                ThemeType.LegalBlue => LegalBlueTheme.Accent,
                _ => LegalClassicTheme.Accent
            };
        }

        public Color GetBackgroundColor()
        {
            return _currentTheme switch
            {
                ThemeType.LegalClassic => LegalClassicTheme.Background,
                ThemeType.LegalModern => LegalModernTheme.Background,
                ThemeType.LegalDark => LegalDarkTheme.Background,
                ThemeType.LegalBlue => LegalBlueTheme.Background,
                _ => LegalClassicTheme.Background
            };
        }

        public Color GetSurfaceColor()
        {
            return _currentTheme switch
            {
                ThemeType.LegalClassic => LegalClassicTheme.Surface,
                ThemeType.LegalModern => LegalModernTheme.Surface,
                ThemeType.LegalDark => LegalDarkTheme.Surface,
                ThemeType.LegalBlue => LegalBlueTheme.Surface,
                _ => LegalClassicTheme.Surface
            };
        }

        public Color GetSurfaceElevatedColor()
        {
            return _currentTheme switch
            {
                ThemeType.LegalClassic => LegalClassicTheme.SurfaceElevated,
                ThemeType.LegalModern => LegalModernTheme.SurfaceElevated,
                ThemeType.LegalDark => LegalDarkTheme.SurfaceElevated,
                ThemeType.LegalBlue => LegalBlueTheme.SurfaceElevated,
                _ => LegalClassicTheme.SurfaceElevated
            };
        }

        public Color GetTextColor()
        {
            return _currentTheme switch
            {
                ThemeType.LegalClassic => LegalClassicTheme.TextPrimary,
                ThemeType.LegalModern => LegalModernTheme.TextPrimary,
                ThemeType.LegalDark => LegalDarkTheme.TextPrimary,
                ThemeType.LegalBlue => LegalBlueTheme.TextPrimary,
                _ => LegalClassicTheme.TextPrimary
            };
        }

        public Color GetTextSecondaryColor()
        {
            return _currentTheme switch
            {
                ThemeType.LegalClassic => LegalClassicTheme.TextSecondary,
                ThemeType.LegalModern => LegalModernTheme.TextSecondary,
                ThemeType.LegalDark => LegalDarkTheme.TextSecondary,
                ThemeType.LegalBlue => LegalBlueTheme.TextSecondary,
                _ => LegalClassicTheme.TextSecondary
            };
        }

        public Color GetTextOnPrimaryColor()
        {
            return _currentTheme switch
            {
                ThemeType.LegalClassic => LegalClassicTheme.TextOnPrimary,
                ThemeType.LegalModern => LegalModernTheme.TextOnPrimary,
                ThemeType.LegalDark => LegalDarkTheme.TextOnPrimary,
                ThemeType.LegalBlue => LegalBlueTheme.TextOnPrimary,
                _ => LegalClassicTheme.TextOnPrimary
            };
        }

        public Color GetBorderColor()
        {
            return _currentTheme switch
            {
                ThemeType.LegalClassic => LegalClassicTheme.Border,
                ThemeType.LegalModern => LegalModernTheme.Border,
                ThemeType.LegalDark => LegalDarkTheme.Border,
                ThemeType.LegalBlue => LegalBlueTheme.Border,
                _ => LegalClassicTheme.Border
            };
        }

        public Color GetSuccessColor()
        {
            return _currentTheme switch
            {
                ThemeType.LegalClassic => LegalClassicTheme.Success,
                ThemeType.LegalModern => LegalModernTheme.Success,
                ThemeType.LegalDark => LegalDarkTheme.Success,
                ThemeType.LegalBlue => LegalBlueTheme.Success,
                _ => LegalClassicTheme.Success
            };
        }

        public Color GetWarningColor()
        {
            return _currentTheme switch
            {
                ThemeType.LegalClassic => LegalClassicTheme.Warning,
                ThemeType.LegalModern => LegalModernTheme.Warning,
                ThemeType.LegalDark => LegalDarkTheme.Warning,
                ThemeType.LegalBlue => LegalBlueTheme.Warning,
                _ => LegalClassicTheme.Warning
            };
        }

        public Color GetErrorColor()
        {
            return _currentTheme switch
            {
                ThemeType.LegalClassic => LegalClassicTheme.Danger,
                ThemeType.LegalModern => LegalModernTheme.Danger,
                ThemeType.LegalDark => LegalDarkTheme.Danger,
                ThemeType.LegalBlue => LegalBlueTheme.Danger,
                _ => LegalClassicTheme.Danger
            };
        }

        public Color GetInfoColor()
        {
            return _currentTheme switch
            {
                ThemeType.LegalClassic => LegalClassicTheme.Info,
                ThemeType.LegalModern => LegalModernTheme.Info,
                ThemeType.LegalDark => LegalDarkTheme.Info,
                ThemeType.LegalBlue => LegalBlueTheme.Info,
                _ => LegalClassicTheme.Info
            };
        }

        public Color GetShadowColor()
        {
            return _currentTheme switch
            {
                ThemeType.LegalClassic => LegalClassicTheme.Shadow,
                ThemeType.LegalModern => LegalModernTheme.Shadow,
                ThemeType.LegalDark => LegalDarkTheme.Shadow,
                ThemeType.LegalBlue => LegalBlueTheme.Shadow,
                _ => LegalClassicTheme.Shadow
            };
        }
    }

    public static class UIHelper
    {
        public static void ApplyRoundedCorners(Control control, int radius = 10)
        {
            GraphicsPath path = new GraphicsPath();
            Rectangle rect = new Rectangle(0, 0, control.Width, control.Height);
            
            path.AddArc(rect.X, rect.Y, radius, radius, 180, 90);
            path.AddArc(rect.X + rect.Width - radius, rect.Y, radius, radius, 270, 90);
            path.AddArc(rect.X + rect.Width - radius, rect.Y + rect.Height - radius, radius, radius, 0, 90);
            path.AddArc(rect.X, rect.Y + rect.Height - radius, radius, radius, 90, 90);
            path.CloseAllFigures();
            
            control.Region = new Region(path);
        }

        public static void AddShadow(Control control, Color shadowColor, int shadowSize = 5, int shadowOpacity = 50)
        {
            // إنشاء تأثير الظل باستخدام Panel خلفي
            Panel shadowPanel = new Panel
            {
                BackColor = Color.FromArgb(shadowOpacity, shadowColor),
                Size = new Size(control.Width + shadowSize, control.Height + shadowSize),
                Location = new Point(control.Location.X + shadowSize, control.Location.Y + shadowSize)
            };
            
            if (control.Parent != null)
            {
                control.Parent.Controls.Add(shadowPanel);
                shadowPanel.SendToBack();
                ApplyRoundedCorners(shadowPanel, 10);
            }
        }

        public static void AnimateControl(Control control, AnimationType animationType, int duration = 300)
        {
            System.Windows.Forms.Timer animationTimer = new System.Windows.Forms.Timer();
            animationTimer.Interval = 16; // ~60 FPS
            
            DateTime startTime = DateTime.Now;
            Point originalLocation = control.Location;
            Size originalSize = control.Size;
            int originalOpacity = 255;
            
            animationTimer.Tick += (sender, e) =>
            {
                double elapsed = (DateTime.Now - startTime).TotalMilliseconds;
                double progress = Math.Min(elapsed / duration, 1.0);
                
                // تطبيق منحنى الحركة (ease-out)
                progress = 1 - Math.Pow(1 - progress, 3);
                
                switch (animationType)
                {
                    case AnimationType.FadeIn:
                        // تأثير الظهور التدريجي
                        break;
                    case AnimationType.SlideInFromLeft:
                        control.Location = new Point(
                            originalLocation.X - (int)((1 - progress) * 200),
                            originalLocation.Y
                        );
                        break;
                    case AnimationType.SlideInFromRight:
                        control.Location = new Point(
                            originalLocation.X + (int)((1 - progress) * 200),
                            originalLocation.Y
                        );
                        break;
                    case AnimationType.ScaleIn:
                        int newWidth = (int)(originalSize.Width * progress);
                        int newHeight = (int)(originalSize.Height * progress);
                        control.Size = new Size(newWidth, newHeight);
                        control.Location = new Point(
                            originalLocation.X + (originalSize.Width - newWidth) / 2,
                            originalLocation.Y + (originalSize.Height - newHeight) / 2
                        );
                        break;
                }
                
                if (progress >= 1.0)
                {
                    animationTimer.Stop();
                    animationTimer.Dispose();
                }
            };
            
            animationTimer.Start();
        }

        public static Button CreateStyledButton(string text, Color backgroundColor, Color textColor, int width = 120, int height = 40)
        {
            Button button = new Button
            {
                Text = text,
                Size = new Size(width, height),
                BackColor = backgroundColor,
                ForeColor = textColor,
                FlatStyle = FlatStyle.Flat,
                Font = new Font("Segoe UI", 10, FontStyle.Bold),
                Cursor = Cursors.Hand,
                TextAlign = ContentAlignment.MiddleCenter
            };
            
            button.FlatAppearance.BorderSize = 0;
            button.FlatAppearance.MouseOverBackColor = ControlPaint.Light(backgroundColor, 0.1f);
            button.FlatAppearance.MouseDownBackColor = ControlPaint.Dark(backgroundColor, 0.1f);
            
            // إضافة تأثيرات الحركة
            button.MouseEnter += (s, e) =>
            {
                button.BackColor = ControlPaint.Light(backgroundColor, 0.1f);
                AnimateButtonHover(button, true);
            };
            
            button.MouseLeave += (s, e) =>
            {
                button.BackColor = backgroundColor;
                AnimateButtonHover(button, false);
            };
            
            ApplyRoundedCorners(button, 8);
            
            return button;
        }

        private static void AnimateButtonHover(Button button, bool isHover)
        {
            System.Windows.Forms.Timer timer = new System.Windows.Forms.Timer();
            timer.Interval = 16;
            
            DateTime startTime = DateTime.Now;
            Size originalSize = button.Size;
            int targetScale = isHover ? 5 : -5;
            
            timer.Tick += (s, e) =>
            {
                double elapsed = (DateTime.Now - startTime).TotalMilliseconds;
                double progress = Math.Min(elapsed / 150, 1.0);
                
                if (!isHover) progress = 1 - progress;
                
                int scaleAmount = (int)(targetScale * progress);
                button.Size = new Size(
                    originalSize.Width + scaleAmount,
                    originalSize.Height + scaleAmount / 2
                );
                
                if (progress >= 1.0)
                {
                    timer.Stop();
                    timer.Dispose();
                }
            };
            
            timer.Start();
        }

        public static Panel CreateStyledPanel(Color backgroundColor, int borderRadius = 10)
        {
            Panel panel = new Panel
            {
                BackColor = backgroundColor,
                Padding = new Padding(15)
            };
            
            ApplyRoundedCorners(panel, borderRadius);
            return panel;
        }

        public static TextBox CreateStyledTextBox(int width = 200, int height = 35)
        {
            TextBox textBox = new TextBox
            {
                Size = new Size(width, height),
                Font = new Font("Segoe UI", 10),
                BorderStyle = BorderStyle.None,
                BackColor = ThemeManager.Instance.GetSurfaceColor(),
                ForeColor = ThemeManager.Instance.GetTextColor()
            };
            
            // إضافة حدود مخصصة
            Panel borderPanel = new Panel
            {
                Size = new Size(width + 2, height + 2),
                BackColor = ThemeManager.Instance.GetBorderColor()
            };
            
            borderPanel.Controls.Add(textBox);
            textBox.Location = new Point(1, 1);
            
            ApplyRoundedCorners(borderPanel, 6);
            ApplyRoundedCorners(textBox, 5);
            
            return textBox;
        }
    }

    public enum AnimationType
    {
        FadeIn,
        FadeOut,
        SlideInFromLeft,
        SlideInFromRight,
        SlideInFromTop,
        SlideInFromBottom,
        ScaleIn,
        ScaleOut
    }
}
