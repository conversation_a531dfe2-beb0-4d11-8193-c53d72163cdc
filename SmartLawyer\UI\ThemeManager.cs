using System;
using System.Drawing;
using System.Drawing.Drawing2D;
using System.Windows.Forms;

namespace SmartLawyer.UI
{
    public enum ThemeType
    {
        Light,
        Dark,
        Blue,
        Green
    }

    public class ThemeManager
    {
        private static ThemeManager _instance;
        private static readonly object _lock = new object();
        private ThemeType _currentTheme = ThemeType.Light;

        private ThemeManager() { }

        public static ThemeManager Instance
        {
            get
            {
                if (_instance == null)
                {
                    lock (_lock)
                    {
                        if (_instance == null)
                            _instance = new ThemeManager();
                    }
                }
                return _instance;
            }
        }

        public ThemeType CurrentTheme
        {
            get { return _currentTheme; }
            set
            {
                _currentTheme = value;
                OnThemeChanged?.Invoke(value);
            }
        }

        public event Action<ThemeType> OnThemeChanged;

        // ألوان الثيم الفاتح
        public static class LightTheme
        {
            public static readonly Color Primary = Color.FromArgb(52, 152, 219);
            public static readonly Color PrimaryDark = Color.FromArgb(41, 128, 185);
            public static readonly Color Secondary = Color.FromArgb(155, 89, 182);
            public static readonly Color Success = Color.FromArgb(46, 204, 113);
            public static readonly Color Warning = Color.FromArgb(241, 196, 15);
            public static readonly Color Danger = Color.FromArgb(231, 76, 60);
            public static readonly Color Info = Color.FromArgb(52, 73, 94);
            
            public static readonly Color Background = Color.FromArgb(248, 249, 250);
            public static readonly Color Surface = Color.White;
            public static readonly Color SurfaceVariant = Color.FromArgb(240, 240, 240);
            
            public static readonly Color TextPrimary = Color.FromArgb(33, 37, 41);
            public static readonly Color TextSecondary = Color.FromArgb(108, 117, 125);
            public static readonly Color TextMuted = Color.FromArgb(173, 181, 189);
            
            public static readonly Color Border = Color.FromArgb(222, 226, 230);
            public static readonly Color BorderLight = Color.FromArgb(233, 236, 239);
            
            public static readonly Color Shadow = Color.FromArgb(50, 0, 0, 0);
            public static readonly Color ShadowLight = Color.FromArgb(25, 0, 0, 0);
        }

        // ألوان الثيم الداكن
        public static class DarkTheme
        {
            public static readonly Color Primary = Color.FromArgb(100, 181, 246);
            public static readonly Color PrimaryDark = Color.FromArgb(66, 165, 245);
            public static readonly Color Secondary = Color.FromArgb(186, 104, 200);
            public static readonly Color Success = Color.FromArgb(102, 187, 106);
            public static readonly Color Warning = Color.FromArgb(255, 193, 7);
            public static readonly Color Danger = Color.FromArgb(244, 67, 54);
            public static readonly Color Info = Color.FromArgb(96, 125, 139);
            
            public static readonly Color Background = Color.FromArgb(18, 18, 18);
            public static readonly Color Surface = Color.FromArgb(33, 33, 33);
            public static readonly Color SurfaceVariant = Color.FromArgb(48, 48, 48);
            
            public static readonly Color TextPrimary = Color.FromArgb(255, 255, 255);
            public static readonly Color TextSecondary = Color.FromArgb(189, 189, 189);
            public static readonly Color TextMuted = Color.FromArgb(117, 117, 117);
            
            public static readonly Color Border = Color.FromArgb(66, 66, 66);
            public static readonly Color BorderLight = Color.FromArgb(82, 82, 82);
            
            public static readonly Color Shadow = Color.FromArgb(100, 0, 0, 0);
            public static readonly Color ShadowLight = Color.FromArgb(50, 0, 0, 0);
        }

        public Color GetPrimaryColor()
        {
            return _currentTheme switch
            {
                ThemeType.Light => LightTheme.Primary,
                ThemeType.Dark => DarkTheme.Primary,
                ThemeType.Blue => Color.FromArgb(25, 118, 210),
                ThemeType.Green => Color.FromArgb(56, 142, 60),
                _ => LightTheme.Primary
            };
        }

        public Color GetBackgroundColor()
        {
            return _currentTheme switch
            {
                ThemeType.Light => LightTheme.Background,
                ThemeType.Dark => DarkTheme.Background,
                ThemeType.Blue => Color.FromArgb(227, 242, 253),
                ThemeType.Green => Color.FromArgb(232, 245, 233),
                _ => LightTheme.Background
            };
        }

        public Color GetSurfaceColor()
        {
            return _currentTheme switch
            {
                ThemeType.Light => LightTheme.Surface,
                ThemeType.Dark => DarkTheme.Surface,
                ThemeType.Blue => Color.FromArgb(250, 250, 250),
                ThemeType.Green => Color.FromArgb(250, 250, 250),
                _ => LightTheme.Surface
            };
        }

        public Color GetTextColor()
        {
            return _currentTheme switch
            {
                ThemeType.Light => LightTheme.TextPrimary,
                ThemeType.Dark => DarkTheme.TextPrimary,
                ThemeType.Blue => Color.FromArgb(13, 60, 97),
                ThemeType.Green => Color.FromArgb(27, 94, 32),
                _ => LightTheme.TextPrimary
            };
        }

        public Color GetSecondaryTextColor()
        {
            return _currentTheme switch
            {
                ThemeType.Light => LightTheme.TextSecondary,
                ThemeType.Dark => DarkTheme.TextSecondary,
                ThemeType.Blue => Color.FromArgb(69, 90, 100),
                ThemeType.Green => Color.FromArgb(69, 90, 100),
                _ => LightTheme.TextSecondary
            };
        }

        public Color GetBorderColor()
        {
            return _currentTheme switch
            {
                ThemeType.Light => LightTheme.Border,
                ThemeType.Dark => DarkTheme.Border,
                ThemeType.Blue => Color.FromArgb(187, 222, 251),
                ThemeType.Green => Color.FromArgb(200, 230, 201),
                _ => LightTheme.Border
            };
        }

        public Color GetShadowColor()
        {
            return _currentTheme switch
            {
                ThemeType.Light => LightTheme.Shadow,
                ThemeType.Dark => DarkTheme.Shadow,
                _ => LightTheme.Shadow
            };
        }
    }

    public static class UIHelper
    {
        public static void ApplyRoundedCorners(Control control, int radius = 10)
        {
            GraphicsPath path = new GraphicsPath();
            Rectangle rect = new Rectangle(0, 0, control.Width, control.Height);
            
            path.AddArc(rect.X, rect.Y, radius, radius, 180, 90);
            path.AddArc(rect.X + rect.Width - radius, rect.Y, radius, radius, 270, 90);
            path.AddArc(rect.X + rect.Width - radius, rect.Y + rect.Height - radius, radius, radius, 0, 90);
            path.AddArc(rect.X, rect.Y + rect.Height - radius, radius, radius, 90, 90);
            path.CloseAllFigures();
            
            control.Region = new Region(path);
        }

        public static void AddShadow(Control control, Color shadowColor, int shadowSize = 5, int shadowOpacity = 50)
        {
            // إنشاء تأثير الظل باستخدام Panel خلفي
            Panel shadowPanel = new Panel
            {
                BackColor = Color.FromArgb(shadowOpacity, shadowColor),
                Size = new Size(control.Width + shadowSize, control.Height + shadowSize),
                Location = new Point(control.Location.X + shadowSize, control.Location.Y + shadowSize)
            };
            
            if (control.Parent != null)
            {
                control.Parent.Controls.Add(shadowPanel);
                shadowPanel.SendToBack();
                ApplyRoundedCorners(shadowPanel, 10);
            }
        }

        public static void AnimateControl(Control control, AnimationType animationType, int duration = 300)
        {
            Timer animationTimer = new Timer();
            animationTimer.Interval = 16; // ~60 FPS
            
            DateTime startTime = DateTime.Now;
            Point originalLocation = control.Location;
            Size originalSize = control.Size;
            int originalOpacity = 255;
            
            animationTimer.Tick += (sender, e) =>
            {
                double elapsed = (DateTime.Now - startTime).TotalMilliseconds;
                double progress = Math.Min(elapsed / duration, 1.0);
                
                // تطبيق منحنى الحركة (ease-out)
                progress = 1 - Math.Pow(1 - progress, 3);
                
                switch (animationType)
                {
                    case AnimationType.FadeIn:
                        // تأثير الظهور التدريجي
                        break;
                    case AnimationType.SlideInFromLeft:
                        control.Location = new Point(
                            originalLocation.X - (int)((1 - progress) * 200),
                            originalLocation.Y
                        );
                        break;
                    case AnimationType.SlideInFromRight:
                        control.Location = new Point(
                            originalLocation.X + (int)((1 - progress) * 200),
                            originalLocation.Y
                        );
                        break;
                    case AnimationType.ScaleIn:
                        int newWidth = (int)(originalSize.Width * progress);
                        int newHeight = (int)(originalSize.Height * progress);
                        control.Size = new Size(newWidth, newHeight);
                        control.Location = new Point(
                            originalLocation.X + (originalSize.Width - newWidth) / 2,
                            originalLocation.Y + (originalSize.Height - newHeight) / 2
                        );
                        break;
                }
                
                if (progress >= 1.0)
                {
                    animationTimer.Stop();
                    animationTimer.Dispose();
                }
            };
            
            animationTimer.Start();
        }

        public static Button CreateStyledButton(string text, Color backgroundColor, Color textColor, int width = 120, int height = 40)
        {
            Button button = new Button
            {
                Text = text,
                Size = new Size(width, height),
                BackColor = backgroundColor,
                ForeColor = textColor,
                FlatStyle = FlatStyle.Flat,
                Font = new Font("Segoe UI", 10, FontStyle.Bold),
                Cursor = Cursors.Hand,
                TextAlign = ContentAlignment.MiddleCenter
            };
            
            button.FlatAppearance.BorderSize = 0;
            button.FlatAppearance.MouseOverBackColor = ControlPaint.Light(backgroundColor, 0.1f);
            button.FlatAppearance.MouseDownBackColor = ControlPaint.Dark(backgroundColor, 0.1f);
            
            // إضافة تأثيرات الحركة
            button.MouseEnter += (s, e) =>
            {
                button.BackColor = ControlPaint.Light(backgroundColor, 0.1f);
                AnimateButtonHover(button, true);
            };
            
            button.MouseLeave += (s, e) =>
            {
                button.BackColor = backgroundColor;
                AnimateButtonHover(button, false);
            };
            
            ApplyRoundedCorners(button, 8);
            
            return button;
        }

        private static void AnimateButtonHover(Button button, bool isHover)
        {
            Timer timer = new Timer();
            timer.Interval = 16;
            
            DateTime startTime = DateTime.Now;
            Size originalSize = button.Size;
            int targetScale = isHover ? 5 : -5;
            
            timer.Tick += (s, e) =>
            {
                double elapsed = (DateTime.Now - startTime).TotalMilliseconds;
                double progress = Math.Min(elapsed / 150, 1.0);
                
                if (!isHover) progress = 1 - progress;
                
                int scaleAmount = (int)(targetScale * progress);
                button.Size = new Size(
                    originalSize.Width + scaleAmount,
                    originalSize.Height + scaleAmount / 2
                );
                
                if (progress >= 1.0)
                {
                    timer.Stop();
                    timer.Dispose();
                }
            };
            
            timer.Start();
        }

        public static Panel CreateStyledPanel(Color backgroundColor, int borderRadius = 10)
        {
            Panel panel = new Panel
            {
                BackColor = backgroundColor,
                Padding = new Padding(15)
            };
            
            ApplyRoundedCorners(panel, borderRadius);
            return panel;
        }

        public static TextBox CreateStyledTextBox(int width = 200, int height = 35)
        {
            TextBox textBox = new TextBox
            {
                Size = new Size(width, height),
                Font = new Font("Segoe UI", 10),
                BorderStyle = BorderStyle.None,
                BackColor = ThemeManager.Instance.GetSurfaceColor(),
                ForeColor = ThemeManager.Instance.GetTextColor()
            };
            
            // إضافة حدود مخصصة
            Panel borderPanel = new Panel
            {
                Size = new Size(width + 2, height + 2),
                BackColor = ThemeManager.Instance.GetBorderColor()
            };
            
            borderPanel.Controls.Add(textBox);
            textBox.Location = new Point(1, 1);
            
            ApplyRoundedCorners(borderPanel, 6);
            ApplyRoundedCorners(textBox, 5);
            
            return textBox;
        }
    }

    public enum AnimationType
    {
        FadeIn,
        FadeOut,
        SlideInFromLeft,
        SlideInFromRight,
        SlideInFromTop,
        SlideInFromBottom,
        ScaleIn,
        ScaleOut
    }
}
