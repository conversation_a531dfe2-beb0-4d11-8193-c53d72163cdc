﻿using SmartLawyer.Localization;
using SmartLawyer.UI;

namespace SmartLawyer;

partial class Form1
{
    /// <summary>
    ///  Required designer variable.
    /// </summary>
    private System.ComponentModel.IContainer components = null;
    private SimpleSlidingSidebar sidePanel;
    private LegalCard mainPanel;
    private LegalCard topPanel;
    // الأزرار الآن جزء من الشريط الجانبي
    private Label lblWelcome;
    private Label lblDateTime;
    private ComboBox cmbLanguage;

    /// <summary>
    ///  Clean up any resources being used.
    /// </summary>
    /// <param name="disposing">true if managed resources should be disposed; otherwise, false.</param>
    protected override void Dispose(bool disposing)
    {
        if (disposing && (components != null))
        {
            components.Dispose();
        }
        base.Dispose(disposing);
    }

    #region Windows Form Designer generated code

    /// <summary>
    ///  Required method for Designer support - do not modify
    ///  the contents of this method with the code editor.
    /// </summary>
    private void InitializeComponent()
    {
        this.SuspendLayout();

        // إعداد النافذة الرئيسية
        this.Text = "Smart Lawyer - برنامج إدارة مكتب المحاماة";
        this.Size = new System.Drawing.Size(1400, 800);
        this.StartPosition = FormStartPosition.CenterScreen;
        this.WindowState = FormWindowState.Maximized;
        this.BackColor = ThemeManager.Instance.GetBackgroundColor();
        this.Load += Form1_Load;

        // اللوحة العلوية (شريط علوي احترافي)
        topPanel = new LegalCard
        {
            Size = new System.Drawing.Size(1400, 80),
            Location = new System.Drawing.Point(0, 0),
            BackColor = ThemeManager.Instance.GetPrimaryColor(),
            Dock = DockStyle.Top,
            BorderRadius = 0,
            HasElevation = true,
            Elevation = 6,
            HasGradient = true,
            GradientStartColor = ThemeManager.Instance.GetPrimaryColor(),
            GradientEndColor = ThemeManager.Instance.GetSecondaryColor()
        };

        // ترحيب المستخدم
        lblWelcome = new Label
        {
            Text = "مرحباً بك في سمارت لوير",
            Location = new System.Drawing.Point(20, 15),
            Size = new System.Drawing.Size(300, 30),
            Font = new System.Drawing.Font("Segoe UI", 14, System.Drawing.FontStyle.Bold),
            ForeColor = System.Drawing.Color.White,
            TextAlign = ContentAlignment.MiddleLeft
        };

        // التاريخ والوقت
        lblDateTime = new Label
        {
            Text = DateTime.Now.ToString("yyyy/MM/dd - HH:mm"),
            Location = new System.Drawing.Point(1100, 15),
            Size = new System.Drawing.Size(200, 30),
            Font = new System.Drawing.Font("Segoe UI", 12),
            ForeColor = System.Drawing.Color.White,
            TextAlign = ContentAlignment.MiddleRight
        };

        // قائمة اللغة
        cmbLanguage = new ComboBox
        {
            Location = new System.Drawing.Point(950, 15),
            Size = new System.Drawing.Size(120, 30),
            Font = new System.Drawing.Font("Segoe UI", 10),
            DropDownStyle = ComboBoxStyle.DropDownList,
            BackColor = System.Drawing.Color.White
        };
        cmbLanguage.Items.Add("العربية");
        cmbLanguage.Items.Add("Français");
        cmbLanguage.SelectedIndex = 0;
        cmbLanguage.SelectedIndexChanged += CmbLanguage_SelectedIndexChanged;

        topPanel.Controls.Add(lblWelcome);
        topPanel.Controls.Add(lblDateTime);
        topPanel.Controls.Add(cmbLanguage);

        // الشريط الجانبي المنزلق الأنيق
        sidePanel = new SimpleSlidingSidebar
        {
            ExpandedWidth = 280,
            CollapsedWidth = 60,
            Dock = DockStyle.Left
        };

        // إضافة عناصر القائمة للشريط الجانبي المنزلق
        sidePanel.AddMenuItem("الرئيسية", "🏠", BtnDashboard_Click);
        sidePanel.AddMenuItem("إدارة الموكلين", "👥", BtnClients_Click);
        sidePanel.AddMenuItem("الملفات والقضايا", "📁", BtnCases_Click);
        sidePanel.AddMenuItem("مذكرة الجلسات", "⚖️", BtnHearings_Click);
        sidePanel.AddMenuItem("المواعيد", "📅", BtnAppointments_Click);
        sidePanel.AddMenuItem("الإجراءات", "📋", BtnProcedures_Click);
        sidePanel.AddMenuItem("مصاريف المكتب", "💰", BtnExpenses_Click);
        sidePanel.AddMenuItem("نظام المستخدمين", "👤", BtnUsers_Click);
        sidePanel.AddMenuItem("الإشعارات", "🔔", BtnNotifications_Click);
        sidePanel.AddMenuItem("التقارير", "📊", BtnReports_Click);
        sidePanel.AddMenuItem("الإعدادات", "⚙️", BtnSettings_Click);
        sidePanel.AddMenuItem("تسجيل الخروج", "🚪", BtnLogout_Click);

        // اللوحة الرئيسية (منطقة المحتوى)
        mainPanel = new LegalCard
        {
            Location = new System.Drawing.Point(280, 80),
            Size = new System.Drawing.Size(1120, 720),
            BackColor = ThemeManager.Instance.GetBackgroundColor(),
            Dock = DockStyle.Fill,
            BorderRadius = 0,
            HasElevation = false,
            Elevation = 0,
            HasGradient = true,
            GradientStartColor = ThemeManager.Instance.GetBackgroundColor(),
            GradientEndColor = ThemeManager.Instance.GetSurfaceColor()
        };

        // إضافة اللوحات إلى النافذة
        this.Controls.Add(topPanel);
        this.Controls.Add(sidePanel);
        this.Controls.Add(mainPanel);

        this.ResumeLayout(false);
    }

    // دالة CreateSimpleButton لم تعد مطلوبة - الشريط الجانبي يدير العناصر تلقائياً

    private void CmbLanguage_SelectedIndexChanged(object sender, EventArgs e)
    {
        string selectedLanguage = cmbLanguage.SelectedIndex == 0 ? "ar" : "fr";
        LanguageManager.Instance.CurrentLanguage = selectedLanguage;
        UpdateLanguage();
    }

    private void UpdateLanguage()
    {
        var lang = LanguageManager.Instance;

        // تحديث النصوص في الشريط الجانبي سيتم من خلال إعادة إنشاء العناصر
        // أو يمكن إضافة دالة تحديث اللغة للشريط الجانبي لاحقاً

        lblWelcome.Text = lang.GetText("Welcome");
    }

    #endregion
}
