﻿using SmartLawyer.Localization;
using SmartLawyer.UI;

namespace SmartLawyer;

partial class Form1
{
    /// <summary>
    ///  Required designer variable.
    /// </summary>
    private System.ComponentModel.IContainer components = null;
    private ModernPanel sidePanel;
    private ModernPanel mainPanel;
    private ModernPanel topPanel;
    private ModernButton btnClients;
    private ModernButton btnCases;
    private ModernButton btnHearings;
    private ModernButton btnAppointments;
    private ModernButton btnProcedures;
    private ModernButton btnExpenses;
    private ModernButton btnUsers;
    private ModernButton btnNotifications;
    private ModernButton btnReports;
    private ModernButton btnSettings;
    private ModernButton btnLogout;
    private Label lblWelcome;
    private Label lblDateTime;
    private ComboBox cmbLanguage;

    /// <summary>
    ///  Clean up any resources being used.
    /// </summary>
    /// <param name="disposing">true if managed resources should be disposed; otherwise, false.</param>
    protected override void Dispose(bool disposing)
    {
        if (disposing && (components != null))
        {
            components.Dispose();
        }
        base.Dispose(disposing);
    }

    #region Windows Form Designer generated code

    /// <summary>
    ///  Required method for Designer support - do not modify
    ///  the contents of this method with the code editor.
    /// </summary>
    private void InitializeComponent()
    {
        this.SuspendLayout();

        // إعداد النافذة الرئيسية
        this.Text = "Smart Lawyer - برنامج إدارة مكتب المحاماة";
        this.Size = new System.Drawing.Size(1400, 800);
        this.StartPosition = FormStartPosition.CenterScreen;
        this.WindowState = FormWindowState.Maximized;
        this.BackColor = ThemeManager.Instance.GetBackgroundColor();
        this.Load += Form1_Load;

        // اللوحة العلوية
        topPanel = new ModernPanel
        {
            Size = new System.Drawing.Size(1400, 60),
            Location = new System.Drawing.Point(0, 0),
            BackColor = ThemeManager.Instance.GetPrimaryColor(),
            Dock = DockStyle.Top,
            BorderRadius = 0,
            HasShadow = true
        };

        // ترحيب المستخدم
        lblWelcome = new Label
        {
            Text = "مرحباً بك في سمارت لوير",
            Location = new System.Drawing.Point(20, 15),
            Size = new System.Drawing.Size(300, 30),
            Font = new System.Drawing.Font("Segoe UI", 14, System.Drawing.FontStyle.Bold),
            ForeColor = System.Drawing.Color.White,
            TextAlign = ContentAlignment.MiddleLeft
        };

        // التاريخ والوقت
        lblDateTime = new Label
        {
            Text = DateTime.Now.ToString("yyyy/MM/dd - HH:mm"),
            Location = new System.Drawing.Point(1100, 15),
            Size = new System.Drawing.Size(200, 30),
            Font = new System.Drawing.Font("Segoe UI", 12),
            ForeColor = System.Drawing.Color.White,
            TextAlign = ContentAlignment.MiddleRight
        };

        // قائمة اللغة
        cmbLanguage = new ComboBox
        {
            Location = new System.Drawing.Point(950, 15),
            Size = new System.Drawing.Size(120, 30),
            Font = new System.Drawing.Font("Segoe UI", 10),
            DropDownStyle = ComboBoxStyle.DropDownList,
            BackColor = System.Drawing.Color.White
        };
        cmbLanguage.Items.Add("العربية");
        cmbLanguage.Items.Add("Français");
        cmbLanguage.SelectedIndex = 0;
        cmbLanguage.SelectedIndexChanged += CmbLanguage_SelectedIndexChanged;

        topPanel.Controls.Add(lblWelcome);
        topPanel.Controls.Add(lblDateTime);
        topPanel.Controls.Add(cmbLanguage);

        // اللوحة الجانبية
        sidePanel = new ModernPanel
        {
            Size = new System.Drawing.Size(250, 740),
            Location = new System.Drawing.Point(0, 60),
            BackColor = System.Drawing.Color.FromArgb(44, 62, 80),
            Dock = DockStyle.Left,
            BorderRadius = 0,
            HasShadow = true
        };

        // أزرار القائمة الجانبية
        int buttonY = 20;
        int buttonHeight = 50;
        int buttonSpacing = 10;

        btnClients = CreateMenuButton("إدارة الموكلين", buttonY);
        buttonY += buttonHeight + buttonSpacing;

        btnCases = CreateMenuButton("الملفات والقضايا", buttonY);
        buttonY += buttonHeight + buttonSpacing;

        btnHearings = CreateMenuButton("مذكرة الجلسات", buttonY);
        buttonY += buttonHeight + buttonSpacing;

        btnAppointments = CreateMenuButton("المواعيد", buttonY);
        buttonY += buttonHeight + buttonSpacing;

        btnProcedures = CreateMenuButton("الإجراءات", buttonY);
        buttonY += buttonHeight + buttonSpacing;

        btnExpenses = CreateMenuButton("مصاريف المكتب", buttonY);
        buttonY += buttonHeight + buttonSpacing;

        btnUsers = CreateMenuButton("نظام المستخدمين", buttonY);
        buttonY += buttonHeight + buttonSpacing;

        btnNotifications = CreateMenuButton("الإشعارات", buttonY);
        buttonY += buttonHeight + buttonSpacing;

        btnReports = CreateMenuButton("التقارير", buttonY);
        buttonY += buttonHeight + buttonSpacing;

        btnSettings = CreateMenuButton("الإعدادات", buttonY);
        buttonY += buttonHeight + buttonSpacing;

        btnLogout = CreateMenuButton("تسجيل الخروج", buttonY);
        btnLogout.BackColor = System.Drawing.Color.FromArgb(231, 76, 60);

        // إضافة الأزرار إلى اللوحة الجانبية
        sidePanel.Controls.Add(btnClients);
        sidePanel.Controls.Add(btnCases);
        sidePanel.Controls.Add(btnHearings);
        sidePanel.Controls.Add(btnAppointments);
        sidePanel.Controls.Add(btnProcedures);
        sidePanel.Controls.Add(btnExpenses);
        sidePanel.Controls.Add(btnUsers);
        sidePanel.Controls.Add(btnNotifications);
        sidePanel.Controls.Add(btnReports);
        sidePanel.Controls.Add(btnSettings);
        sidePanel.Controls.Add(btnLogout);

        // اللوحة الرئيسية
        mainPanel = new ModernPanel
        {
            Location = new System.Drawing.Point(250, 60),
            Size = new System.Drawing.Size(1150, 740),
            BackColor = ThemeManager.Instance.GetSurfaceColor(),
            Dock = DockStyle.Fill,
            BorderRadius = 0,
            HasShadow = false
        };

        // إضافة اللوحات إلى النافذة
        this.Controls.Add(topPanel);
        this.Controls.Add(sidePanel);
        this.Controls.Add(mainPanel);

        this.ResumeLayout(false);
    }

    private ModernButton CreateMenuButton(string text, int y)
    {
        ModernButton btn = new ModernButton
        {
            Text = text,
            Location = new System.Drawing.Point(10, y),
            Size = new System.Drawing.Size(230, 50),
            Font = new System.Drawing.Font("Segoe UI", 12, System.Drawing.FontStyle.Bold),
            BackColor = System.Drawing.Color.FromArgb(52, 152, 219),
            ForeColor = System.Drawing.Color.White,
            BorderRadius = 8
        };

        // إضافة تأثيرات الحركة
        btn.MouseEnter += (s, e) =>
        {
            AnimationManager.ScaleIn(btn, 150);
        };

        return btn;
    }

    private void CmbLanguage_SelectedIndexChanged(object sender, EventArgs e)
    {
        string selectedLanguage = cmbLanguage.SelectedIndex == 0 ? "ar" : "fr";
        LanguageManager.Instance.CurrentLanguage = selectedLanguage;
        UpdateLanguage();
    }

    private void UpdateLanguage()
    {
        var lang = LanguageManager.Instance;

        btnClients.Text = lang.GetText("Clients");
        btnCases.Text = lang.GetText("Cases");
        btnHearings.Text = lang.GetText("Hearings");
        btnAppointments.Text = lang.GetText("Appointments");
        btnProcedures.Text = lang.GetText("Procedures");
        btnExpenses.Text = lang.GetText("Expenses");
        btnUsers.Text = lang.GetText("Users");
        btnNotifications.Text = lang.GetText("Notifications");
        btnReports.Text = lang.GetText("Reports");
        btnSettings.Text = lang.GetText("Settings");
        btnLogout.Text = lang.GetText("Logout");
    }

    #endregion
}
