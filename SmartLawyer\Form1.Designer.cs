using SmartLawyer.Localization;
using SmartLawyer.UI;

namespace SmartLawyer;

partial class Form1
{
    /// <summary>
    ///  Required designer variable.
    /// </summary>
    private System.ComponentModel.IContainer components = null;
    private Panel sidePanel;
    private LegalCard mainPanel;
    private LegalCard topPanel;
    // الأزرار الآن جزء من الشريط الجانبي
    private Label lblWelcome;
    private Label lblDateTime;
    private ComboBox cmbLanguage;

    /// <summary>
    ///  Clean up any resources being used.
    /// </summary>
    /// <param name="disposing">true if managed resources should be disposed; otherwise, false.</param>
    protected override void Dispose(bool disposing)
    {
        if (disposing && (components != null))
        {
            components.Dispose();
        }
        base.Dispose(disposing);
    }

    #region Windows Form Designer generated code

    /// <summary>
    ///  Required method for Designer support - do not modify
    ///  the contents of this method with the code editor.
    /// </summary>
    private void InitializeComponent()
    {
        this.SuspendLayout();

        // إعداد النافذة الرئيسية
        this.Text = "Smart Lawyer - برنامج إدارة مكتب المحاماة";
        this.Size = new System.Drawing.Size(1400, 800);
        this.StartPosition = FormStartPosition.CenterScreen;
        this.WindowState = FormWindowState.Maximized;
        this.BackColor = ThemeManager.Instance.GetBackgroundColor();
        this.Load += Form1_Load;

        // اللوحة العلوية (شريط علوي احترافي)
        topPanel = new LegalCard
        {
            Size = new System.Drawing.Size(1400, 80),
            Location = new System.Drawing.Point(0, 0),
            BackColor = ThemeManager.Instance.GetPrimaryColor(),
            Dock = DockStyle.Top,
            BorderRadius = 0,
            HasElevation = true,
            Elevation = 6,
            HasGradient = true,
            GradientStartColor = ThemeManager.Instance.GetPrimaryColor(),
            GradientEndColor = ThemeManager.Instance.GetSecondaryColor()
        };

        // ترحيب المستخدم
        lblWelcome = new Label
        {
            Text = "مرحباً بك في سمارت لوير",
            Location = new System.Drawing.Point(20, 15),
            Size = new System.Drawing.Size(300, 30),
            Font = new System.Drawing.Font("Segoe UI", 14, System.Drawing.FontStyle.Bold),
            ForeColor = System.Drawing.Color.White,
            TextAlign = ContentAlignment.MiddleLeft
        };

        // التاريخ والوقت
        lblDateTime = new Label
        {
            Text = DateTime.Now.ToString("yyyy/MM/dd - HH:mm"),
            Location = new System.Drawing.Point(1100, 15),
            Size = new System.Drawing.Size(200, 30),
            Font = new System.Drawing.Font("Segoe UI", 12),
            ForeColor = System.Drawing.Color.White,
            TextAlign = ContentAlignment.MiddleRight
        };

        // قائمة اللغة
        cmbLanguage = new ComboBox
        {
            Location = new System.Drawing.Point(950, 15),
            Size = new System.Drawing.Size(120, 30),
            Font = new System.Drawing.Font("Segoe UI", 10),
            DropDownStyle = ComboBoxStyle.DropDownList,
            BackColor = System.Drawing.Color.White
        };
        cmbLanguage.Items.Add("العربية");
        cmbLanguage.Items.Add("Français");
        cmbLanguage.SelectedIndex = 0;
        cmbLanguage.SelectedIndexChanged += CmbLanguage_SelectedIndexChanged;

        topPanel.Controls.Add(lblWelcome);
        topPanel.Controls.Add(lblDateTime);
        topPanel.Controls.Add(cmbLanguage);

        // الشريط الجانبي المؤقت (سنحسنه لاحقاً)
        sidePanel = new Panel
        {
            Size = new System.Drawing.Size(280, 720),
            Location = new System.Drawing.Point(0, 80),
            BackColor = Color.FromArgb(44, 62, 80),
            Dock = DockStyle.Left
        };

        // إضافة أزرار للشريط الجانبي
        var btnDashboard = CreateMenuButton("🏠 الرئيسية", 20);
        var btnClients = CreateMenuButton("👥 إدارة الموكلين", 80);
        var btnCases = CreateMenuButton("📁 الملفات والقضايا", 140);
        var btnHearings = CreateMenuButton("⚖️ مذكرة الجلسات", 200);
        var btnAppointments = CreateMenuButton("📅 المواعيد", 260);
        var btnProcedures = CreateMenuButton("📋 الإجراءات", 320);
        var btnExpenses = CreateMenuButton("💰 مصاريف المكتب", 380);
        var btnUsers = CreateMenuButton("👤 نظام المستخدمين", 440);
        var btnNotifications = CreateMenuButton("🔔 الإشعارات", 500);
        var btnReports = CreateMenuButton("📊 التقارير", 560);
        var btnSettings = CreateMenuButton("⚙️ الإعدادات", 620);
        var btnLogout = CreateMenuButton("🚪 تسجيل الخروج", 680);

        // إضافة الأحداث
        btnDashboard.Click += BtnDashboard_Click;
        btnClients.Click += BtnClients_Click;
        btnCases.Click += BtnCases_Click;
        btnHearings.Click += BtnHearings_Click;
        btnAppointments.Click += BtnAppointments_Click;
        btnProcedures.Click += BtnProcedures_Click;
        btnExpenses.Click += BtnExpenses_Click;
        btnUsers.Click += BtnUsers_Click;
        btnNotifications.Click += BtnNotifications_Click;
        btnReports.Click += BtnReports_Click;
        btnSettings.Click += BtnSettings_Click;
        btnLogout.Click += BtnLogout_Click;

        // إضافة الأزرار للوحة
        sidePanel.Controls.Add(btnDashboard);
        sidePanel.Controls.Add(btnClients);
        sidePanel.Controls.Add(btnCases);
        sidePanel.Controls.Add(btnHearings);
        sidePanel.Controls.Add(btnAppointments);
        sidePanel.Controls.Add(btnProcedures);
        sidePanel.Controls.Add(btnExpenses);
        sidePanel.Controls.Add(btnUsers);
        sidePanel.Controls.Add(btnNotifications);
        sidePanel.Controls.Add(btnReports);
        sidePanel.Controls.Add(btnSettings);
        sidePanel.Controls.Add(btnLogout);

        // اللوحة الرئيسية (منطقة المحتوى)
        mainPanel = new LegalCard
        {
            Location = new System.Drawing.Point(280, 80),
            Size = new System.Drawing.Size(1120, 720),
            BackColor = ThemeManager.Instance.GetBackgroundColor(),
            Dock = DockStyle.Fill,
            BorderRadius = 0,
            HasElevation = false,
            Elevation = 0,
            HasGradient = true,
            GradientStartColor = ThemeManager.Instance.GetBackgroundColor(),
            GradientEndColor = ThemeManager.Instance.GetSurfaceColor()
        };

        // إضافة اللوحات إلى النافذة
        this.Controls.Add(topPanel);
        this.Controls.Add(sidePanel);
        this.Controls.Add(mainPanel);

        this.ResumeLayout(false);
    }

    private Button CreateMenuButton(string text, int y)
    {
        var button = new Button
        {
            Text = text,
            Location = new System.Drawing.Point(20, y),
            Size = new System.Drawing.Size(240, 50),
            Font = new System.Drawing.Font("Segoe UI", 11, System.Drawing.FontStyle.Regular),
            BackColor = Color.FromArgb(52, 73, 94),
            ForeColor = Color.White,
            FlatStyle = FlatStyle.Flat,
            TextAlign = ContentAlignment.MiddleLeft,
            Padding = new Padding(15, 0, 0, 0),
            Cursor = Cursors.Hand
        };

        button.FlatAppearance.BorderSize = 0;
        button.FlatAppearance.MouseOverBackColor = ThemeManager.Instance.GetAccentColor();
        button.FlatAppearance.MouseDownBackColor = ControlPaint.Dark(ThemeManager.Instance.GetAccentColor(), 0.1f);

        return button;
    }

    private void CmbLanguage_SelectedIndexChanged(object sender, EventArgs e)
    {
        string selectedLanguage = cmbLanguage.SelectedIndex == 0 ? "ar" : "fr";
        LanguageManager.Instance.CurrentLanguage = selectedLanguage;
        UpdateLanguage();
    }

    private void UpdateLanguage()
    {
        var lang = LanguageManager.Instance;

        // تحديث النصوص في الشريط الجانبي سيتم من خلال إعادة إنشاء العناصر
        // أو يمكن إضافة دالة تحديث اللغة للشريط الجانبي لاحقاً

        lblWelcome.Text = lang.GetText("Welcome");
    }

    #endregion
}
