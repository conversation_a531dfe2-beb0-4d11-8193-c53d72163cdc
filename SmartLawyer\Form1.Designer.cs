﻿using SmartLawyer.Localization;
using SmartLawyer.UI;

namespace SmartLawyer;

partial class Form1
{
    /// <summary>
    ///  Required designer variable.
    /// </summary>
    private System.ComponentModel.IContainer components = null;
    private LegalCard sidePanel;
    private LegalCard mainPanel;
    private LegalCard topPanel;
    private LegalButton btnClients;
    private LegalButton btnCases;
    private LegalButton btnHearings;
    private LegalButton btnAppointments;
    private LegalButton btnProcedures;
    private LegalButton btnExpenses;
    private LegalButton btnUsers;
    private LegalButton btnNotifications;
    private LegalButton btnReports;
    private LegalButton btnSettings;
    private LegalButton btnLogout;
    private Label lblWelcome;
    private Label lblDateTime;
    private ComboBox cmbLanguage;

    /// <summary>
    ///  Clean up any resources being used.
    /// </summary>
    /// <param name="disposing">true if managed resources should be disposed; otherwise, false.</param>
    protected override void Dispose(bool disposing)
    {
        if (disposing && (components != null))
        {
            components.Dispose();
        }
        base.Dispose(disposing);
    }

    #region Windows Form Designer generated code

    /// <summary>
    ///  Required method for Designer support - do not modify
    ///  the contents of this method with the code editor.
    /// </summary>
    private void InitializeComponent()
    {
        this.SuspendLayout();

        // إعداد النافذة الرئيسية
        this.Text = "Smart Lawyer - برنامج إدارة مكتب المحاماة";
        this.Size = new System.Drawing.Size(1400, 800);
        this.StartPosition = FormStartPosition.CenterScreen;
        this.WindowState = FormWindowState.Maximized;
        this.BackColor = ThemeManager.Instance.GetBackgroundColor();
        this.Load += Form1_Load;

        // اللوحة العلوية (شريط علوي احترافي)
        topPanel = new LegalCard
        {
            Size = new System.Drawing.Size(1400, 80),
            Location = new System.Drawing.Point(0, 0),
            BackColor = ThemeManager.Instance.GetPrimaryColor(),
            Dock = DockStyle.Top,
            BorderRadius = 0,
            HasElevation = true,
            Elevation = 6,
            HasGradient = true,
            GradientStartColor = ThemeManager.Instance.GetPrimaryColor(),
            GradientEndColor = ThemeManager.Instance.GetSecondaryColor()
        };

        // ترحيب المستخدم
        lblWelcome = new Label
        {
            Text = "مرحباً بك في سمارت لوير",
            Location = new System.Drawing.Point(20, 15),
            Size = new System.Drawing.Size(300, 30),
            Font = new System.Drawing.Font("Segoe UI", 14, System.Drawing.FontStyle.Bold),
            ForeColor = System.Drawing.Color.White,
            TextAlign = ContentAlignment.MiddleLeft
        };

        // التاريخ والوقت
        lblDateTime = new Label
        {
            Text = DateTime.Now.ToString("yyyy/MM/dd - HH:mm"),
            Location = new System.Drawing.Point(1100, 15),
            Size = new System.Drawing.Size(200, 30),
            Font = new System.Drawing.Font("Segoe UI", 12),
            ForeColor = System.Drawing.Color.White,
            TextAlign = ContentAlignment.MiddleRight
        };

        // قائمة اللغة
        cmbLanguage = new ComboBox
        {
            Location = new System.Drawing.Point(950, 15),
            Size = new System.Drawing.Size(120, 30),
            Font = new System.Drawing.Font("Segoe UI", 10),
            DropDownStyle = ComboBoxStyle.DropDownList,
            BackColor = System.Drawing.Color.White
        };
        cmbLanguage.Items.Add("العربية");
        cmbLanguage.Items.Add("Français");
        cmbLanguage.SelectedIndex = 0;
        cmbLanguage.SelectedIndexChanged += CmbLanguage_SelectedIndexChanged;

        topPanel.Controls.Add(lblWelcome);
        topPanel.Controls.Add(lblDateTime);
        topPanel.Controls.Add(cmbLanguage);

        // اللوحة الجانبية (قائمة تنقل احترافية)
        sidePanel = new LegalCard
        {
            Size = new System.Drawing.Size(280, 720),
            Location = new System.Drawing.Point(0, 80),
            BackColor = ThemeManager.Instance.GetSurfaceElevatedColor(),
            Dock = DockStyle.Left,
            BorderRadius = 0,
            HasElevation = true,
            Elevation = 4,
            HasGradient = true,
            GradientStartColor = ThemeManager.Instance.GetSurfaceElevatedColor(),
            GradientEndColor = ThemeManager.Instance.GetSurfaceColor()
        };

        // أزرار القائمة الجانبية
        int buttonY = 20;
        int buttonHeight = 50;
        int buttonSpacing = 10;

        btnClients = CreateMenuButton("إدارة الموكلين", buttonY);
        buttonY += buttonHeight + buttonSpacing;

        btnCases = CreateMenuButton("الملفات والقضايا", buttonY);
        buttonY += buttonHeight + buttonSpacing;

        btnHearings = CreateMenuButton("مذكرة الجلسات", buttonY);
        buttonY += buttonHeight + buttonSpacing;

        btnAppointments = CreateMenuButton("المواعيد", buttonY);
        buttonY += buttonHeight + buttonSpacing;

        btnProcedures = CreateMenuButton("الإجراءات", buttonY);
        buttonY += buttonHeight + buttonSpacing;

        btnExpenses = CreateMenuButton("مصاريف المكتب", buttonY);
        buttonY += buttonHeight + buttonSpacing;

        btnUsers = CreateMenuButton("نظام المستخدمين", buttonY);
        buttonY += buttonHeight + buttonSpacing;

        btnNotifications = CreateMenuButton("الإشعارات", buttonY);
        buttonY += buttonHeight + buttonSpacing;

        btnReports = CreateMenuButton("التقارير", buttonY);
        buttonY += buttonHeight + buttonSpacing;

        btnSettings = CreateMenuButton("الإعدادات", buttonY);
        buttonY += buttonHeight + buttonSpacing;

        btnLogout = CreateMenuButton("تسجيل الخروج", buttonY);
        btnLogout.BackColor = System.Drawing.Color.FromArgb(231, 76, 60);

        // إضافة الأزرار إلى اللوحة الجانبية
        sidePanel.Controls.Add(btnClients);
        sidePanel.Controls.Add(btnCases);
        sidePanel.Controls.Add(btnHearings);
        sidePanel.Controls.Add(btnAppointments);
        sidePanel.Controls.Add(btnProcedures);
        sidePanel.Controls.Add(btnExpenses);
        sidePanel.Controls.Add(btnUsers);
        sidePanel.Controls.Add(btnNotifications);
        sidePanel.Controls.Add(btnReports);
        sidePanel.Controls.Add(btnSettings);
        sidePanel.Controls.Add(btnLogout);

        // اللوحة الرئيسية (منطقة المحتوى)
        mainPanel = new LegalCard
        {
            Location = new System.Drawing.Point(280, 80),
            Size = new System.Drawing.Size(1120, 720),
            BackColor = ThemeManager.Instance.GetBackgroundColor(),
            Dock = DockStyle.Fill,
            BorderRadius = 0,
            HasElevation = false,
            Elevation = 0,
            HasGradient = true,
            GradientStartColor = ThemeManager.Instance.GetBackgroundColor(),
            GradientEndColor = ThemeManager.Instance.GetSurfaceColor()
        };

        // إضافة اللوحات إلى النافذة
        this.Controls.Add(topPanel);
        this.Controls.Add(sidePanel);
        this.Controls.Add(mainPanel);

        this.ResumeLayout(false);
    }

    private LegalButton CreateMenuButton(string text, int y)
    {
        // تحديد الأيقونة بناءً على النص
        string iconName = text switch
        {
            "إدارة الموكلين" => "clients",
            "الملفات والقضايا" => "cases",
            "مذكرة الجلسات" => "hearings",
            "المواعيد" => "appointments",
            "الإجراءات" => "procedures",
            "مصاريف المكتب" => "expenses",
            "إدارة المستخدمين" => "users",
            "التنبيهات" => "notifications",
            "التقارير" => "reports",
            "الإعدادات" => "settings",
            "تسجيل الخروج" => "logout",
            _ => "law"
        };

        LegalButton btn = new LegalButton
        {
            Text = text,
            Location = new System.Drawing.Point(20, y),
            Size = new System.Drawing.Size(240, 60),
            Font = new System.Drawing.Font("Segoe UI", 13, System.Drawing.FontStyle.Bold),
            BackColor = ThemeManager.Instance.GetSurfaceColor(),
            ForeColor = ThemeManager.Instance.GetTextColor(),
            BorderRadius = 12,
            HasElevation = true,
            Elevation = 2,
            IconName = iconName,
            IconSize = 28
        };

        // إضافة تأثيرات الحركة المتقدمة
        btn.MouseEnter += (s, e) =>
        {
            AnimationManager.ScaleIn(btn, 150);
            btn.BackColor = ThemeManager.Instance.GetPrimaryColor();
            btn.ForeColor = ThemeManager.Instance.GetTextOnPrimaryColor();
            btn.Elevation = 4;
        };

        btn.MouseLeave += (s, e) =>
        {
            btn.BackColor = ThemeManager.Instance.GetSurfaceColor();
            btn.ForeColor = ThemeManager.Instance.GetTextColor();
            btn.Elevation = 2;
        };

        btn.MouseDown += (s, e) =>
        {
            var mousePos = btn.PointToClient(Cursor.Position);
            AdvancedAnimations.CreateRippleEffect(btn, mousePos,
                ThemeManager.Instance.GetAccentColor(), 400);
        };

        return btn;
    }

    private void CmbLanguage_SelectedIndexChanged(object sender, EventArgs e)
    {
        string selectedLanguage = cmbLanguage.SelectedIndex == 0 ? "ar" : "fr";
        LanguageManager.Instance.CurrentLanguage = selectedLanguage;
        UpdateLanguage();
    }

    private void UpdateLanguage()
    {
        var lang = LanguageManager.Instance;

        btnClients.Text = lang.GetText("Clients");
        btnCases.Text = lang.GetText("Cases");
        btnHearings.Text = lang.GetText("Hearings");
        btnAppointments.Text = lang.GetText("Appointments");
        btnProcedures.Text = lang.GetText("Procedures");
        btnExpenses.Text = lang.GetText("Expenses");
        btnUsers.Text = lang.GetText("Users");
        btnNotifications.Text = lang.GetText("Notifications");
        btnReports.Text = lang.GetText("Reports");
        btnSettings.Text = lang.GetText("Settings");
        btnLogout.Text = lang.GetText("Logout");
    }

    #endregion
}
