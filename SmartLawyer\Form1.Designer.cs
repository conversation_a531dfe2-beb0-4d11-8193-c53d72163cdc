﻿using SmartLawyer.Localization;
using SmartLawyer.UI;

namespace SmartLawyer;

partial class Form1
{
    /// <summary>
    ///  Required designer variable.
    /// </summary>
    private System.ComponentModel.IContainer components = null;
    private Panel sidePanel;
    private LegalCard mainPanel;
    private LegalCard topPanel;
    // الأزرار الآن جزء من الشريط الجانبي
    private Label lblWelcome;
    private Label lblDateTime;
    private ComboBox cmbLanguage;

    /// <summary>
    ///  Clean up any resources being used.
    /// </summary>
    /// <param name="disposing">true if managed resources should be disposed; otherwise, false.</param>
    protected override void Dispose(bool disposing)
    {
        if (disposing && (components != null))
        {
            components.Dispose();
        }
        base.Dispose(disposing);
    }

    #region Windows Form Designer generated code

    /// <summary>
    ///  Required method for Designer support - do not modify
    ///  the contents of this method with the code editor.
    /// </summary>
    private void InitializeComponent()
    {
        this.SuspendLayout();

        // إعداد النافذة الرئيسية
        this.Text = "Smart Lawyer - برنامج إدارة مكتب المحاماة";
        this.Size = new System.Drawing.Size(1400, 800);
        this.StartPosition = FormStartPosition.CenterScreen;
        this.WindowState = FormWindowState.Maximized;
        this.BackColor = ThemeManager.Instance.GetBackgroundColor();
        this.Load += Form1_Load;

        // اللوحة العلوية (شريط علوي متطور جداً)
        topPanel = new LegalCard
        {
            Size = new System.Drawing.Size(1400, 90), // أطول قليلاً
            Location = new System.Drawing.Point(0, 0),
            BackColor = Color.FromArgb(15, 23, 42), // لون داكن متطور
            Dock = DockStyle.Top,
            BorderRadius = 0,
            HasElevation = true,
            Elevation = 8, // ظل أقوى
            HasGradient = true,
            GradientStartColor = Color.FromArgb(15, 23, 42),
            GradientEndColor = Color.FromArgb(30, 41, 59)
        };

        // ترحيب المستخدم - تصميم متطور
        lblWelcome = new Label
        {
            Text = "🏛️ نظام المحامي الذكي - Smart Lawyer",
            Location = new System.Drawing.Point(30, 20),
            Size = new System.Drawing.Size(500, 40),
            Font = new System.Drawing.Font("Segoe UI", 16, System.Drawing.FontStyle.Bold),
            ForeColor = Color.FromArgb(226, 232, 240), // نص فاتح أنيق
            TextAlign = ContentAlignment.MiddleLeft
        };

        // التاريخ والوقت - تصميم متطور
        lblDateTime = new Label
        {
            Text = "📅 " + DateTime.Now.ToString("yyyy/MM/dd - HH:mm"),
            Location = new System.Drawing.Point(1050, 25),
            Size = new System.Drawing.Size(250, 40),
            Font = new System.Drawing.Font("Segoe UI", 13, System.Drawing.FontStyle.Regular),
            ForeColor = Color.FromArgb(148, 163, 184), // لون رمادي أنيق
            TextAlign = ContentAlignment.MiddleRight
        };

        // قائمة اللغة
        cmbLanguage = new ComboBox
        {
            Location = new System.Drawing.Point(950, 15),
            Size = new System.Drawing.Size(120, 30),
            Font = new System.Drawing.Font("Segoe UI", 10),
            DropDownStyle = ComboBoxStyle.DropDownList,
            BackColor = System.Drawing.Color.White
        };
        cmbLanguage.Items.Add("العربية");
        cmbLanguage.Items.Add("Français");
        cmbLanguage.SelectedIndex = 0;
        cmbLanguage.SelectedIndexChanged += CmbLanguage_SelectedIndexChanged;

        topPanel.Controls.Add(lblWelcome);
        topPanel.Controls.Add(lblDateTime);
        topPanel.Controls.Add(cmbLanguage);

        // الشريط الجانبي المتطور والأنيق
        sidePanel = new Panel
        {
            Size = new System.Drawing.Size(320, 720),
            Location = new System.Drawing.Point(0, 80),
            BackColor = Color.FromArgb(15, 23, 42), // لون داكن أنيق
            Dock = DockStyle.Left
        };

        // إضافة تأثير الظل للشريط الجانبي
        sidePanel.Paint += SidePanel_Paint;

        // إضافة أزرار للشريط الجانبي
        var btnDashboard = CreateMenuButton("🏠 الرئيسية", 20);
        var btnClients = CreateMenuButton("👥 إدارة الموكلين", 80);
        var btnCases = CreateMenuButton("📁 الملفات والقضايا", 140);
        var btnHearings = CreateMenuButton("⚖️ مذكرة الجلسات", 200);
        var btnAppointments = CreateMenuButton("📅 المواعيد", 260);
        var btnProcedures = CreateMenuButton("📋 الإجراءات", 320);
        var btnExpenses = CreateMenuButton("💰 مصاريف المكتب", 380);
        var btnUsers = CreateMenuButton("👤 نظام المستخدمين", 440);
        var btnNotifications = CreateMenuButton("🔔 الإشعارات", 500);
        var btnReports = CreateMenuButton("📊 التقارير", 560);
        var btnSettings = CreateMenuButton("⚙️ الإعدادات", 620);
        var btnLogout = CreateMenuButton("🚪 تسجيل الخروج", 680);

        // إضافة الأحداث
        btnDashboard.Click += BtnDashboard_Click;
        btnClients.Click += BtnClients_Click;
        btnCases.Click += BtnCases_Click;
        btnHearings.Click += BtnHearings_Click;
        btnAppointments.Click += BtnAppointments_Click;
        btnProcedures.Click += BtnProcedures_Click;
        btnExpenses.Click += BtnExpenses_Click;
        btnUsers.Click += BtnUsers_Click;
        btnNotifications.Click += BtnNotifications_Click;
        btnReports.Click += BtnReports_Click;
        btnSettings.Click += BtnSettings_Click;
        btnLogout.Click += BtnLogout_Click;

        // إضافة الأزرار للوحة
        sidePanel.Controls.Add(btnDashboard);
        sidePanel.Controls.Add(btnClients);
        sidePanel.Controls.Add(btnCases);
        sidePanel.Controls.Add(btnHearings);
        sidePanel.Controls.Add(btnAppointments);
        sidePanel.Controls.Add(btnProcedures);
        sidePanel.Controls.Add(btnExpenses);
        sidePanel.Controls.Add(btnUsers);
        sidePanel.Controls.Add(btnNotifications);
        sidePanel.Controls.Add(btnReports);
        sidePanel.Controls.Add(btnSettings);
        sidePanel.Controls.Add(btnLogout);

        // اللوحة الرئيسية (منطقة المحتوى)
        mainPanel = new LegalCard
        {
            Location = new System.Drawing.Point(280, 80),
            Size = new System.Drawing.Size(1120, 720),
            BackColor = ThemeManager.Instance.GetBackgroundColor(),
            Dock = DockStyle.Fill,
            BorderRadius = 0,
            HasElevation = false,
            Elevation = 0,
            HasGradient = true,
            GradientStartColor = ThemeManager.Instance.GetBackgroundColor(),
            GradientEndColor = ThemeManager.Instance.GetSurfaceColor()
        };

        // إضافة اللوحات إلى النافذة
        this.Controls.Add(topPanel);
        this.Controls.Add(sidePanel);
        this.Controls.Add(mainPanel);

        this.ResumeLayout(false);
    }

    private Button CreateMenuButton(string text, int y)
    {
        var button = new Button
        {
            Text = text,
            Location = new System.Drawing.Point(25, y),
            Size = new System.Drawing.Size(270, 60), // أزرار أكبر وأكثر أناقة
            Font = new System.Drawing.Font("Segoe UI", 12, System.Drawing.FontStyle.Bold),
            BackColor = Color.FromArgb(30, 41, 59), // لون أزرق داكن أنيق
            ForeColor = Color.FromArgb(226, 232, 240), // نص فاتح
            FlatStyle = FlatStyle.Flat,
            TextAlign = ContentAlignment.MiddleLeft,
            Padding = new Padding(20, 0, 0, 0),
            Cursor = Cursors.Hand,
            UseVisualStyleBackColor = false
        };

        // تحسينات بصرية متطورة
        button.FlatAppearance.BorderSize = 0;
        button.FlatAppearance.MouseOverBackColor = Color.FromArgb(59, 130, 246); // أزرق حديث
        button.FlatAppearance.MouseDownBackColor = Color.FromArgb(37, 99, 235);

        // إضافة تأثيرات الحواف المدورة
        button.Paint += (s, e) => DrawModernButton(e.Graphics, button);

        // تأثيرات الماوس المتطورة
        button.MouseEnter += (s, e) => {
            button.BackColor = Color.FromArgb(59, 130, 246);
            button.ForeColor = Color.White;
        };

        button.MouseLeave += (s, e) => {
            button.BackColor = Color.FromArgb(30, 41, 59);
            button.ForeColor = Color.FromArgb(226, 232, 240);
        };

        return button;
    }

    private void DrawModernButton(Graphics g, Button button)
    {
        // رسم زر بحواف مدورة وظلال
        g.SmoothingMode = System.Drawing.Drawing2D.SmoothingMode.AntiAlias;

        var rect = new Rectangle(5, 5, button.Width - 10, button.Height - 10);
        var radius = 15;

        using (var path = GetRoundedRectanglePath(rect, radius))
        {
            // رسم الظل
            using (var shadowBrush = new SolidBrush(Color.FromArgb(50, 0, 0, 0)))
            {
                var shadowRect = new Rectangle(rect.X + 2, rect.Y + 2, rect.Width, rect.Height);
                using (var shadowPath = GetRoundedRectanglePath(shadowRect, radius))
                {
                    g.FillPath(shadowBrush, shadowPath);
                }
            }

            // رسم الخلفية
            using (var brush = new SolidBrush(button.BackColor))
            {
                g.FillPath(brush, path);
            }

            // رسم الحدود
            using (var pen = new Pen(Color.FromArgb(100, 255, 255, 255), 1))
            {
                g.DrawPath(pen, path);
            }
        }
    }

    private System.Drawing.Drawing2D.GraphicsPath GetRoundedRectanglePath(Rectangle rect, int radius)
    {
        var path = new System.Drawing.Drawing2D.GraphicsPath();
        path.AddArc(rect.X, rect.Y, radius, radius, 180, 90);
        path.AddArc(rect.X + rect.Width - radius, rect.Y, radius, radius, 270, 90);
        path.AddArc(rect.X + rect.Width - radius, rect.Y + rect.Height - radius, radius, radius, 0, 90);
        path.AddArc(rect.X, rect.Y + rect.Height - radius, radius, radius, 90, 90);
        path.CloseAllFigures();
        return path;
    }

    private void SidePanel_Paint(object sender, PaintEventArgs e)
    {
        // رسم خلفية متدرجة للشريط الجانبي
        Graphics g = e.Graphics;
        g.SmoothingMode = System.Drawing.Drawing2D.SmoothingMode.AntiAlias;

        using (var brush = new System.Drawing.Drawing2D.LinearGradientBrush(
            new Rectangle(0, 0, sidePanel.Width, sidePanel.Height),
            Color.FromArgb(15, 23, 42),
            Color.FromArgb(30, 41, 59),
            System.Drawing.Drawing2D.LinearGradientMode.Vertical))
        {
            g.FillRectangle(brush, new Rectangle(0, 0, sidePanel.Width, sidePanel.Height));
        }

        // رسم خط فاصل أنيق
        using (var pen = new Pen(Color.FromArgb(100, 255, 255, 255), 1))
        {
            g.DrawLine(pen, sidePanel.Width - 1, 0, sidePanel.Width - 1, sidePanel.Height);
        }
    }

    private void CmbLanguage_SelectedIndexChanged(object sender, EventArgs e)
    {
        string selectedLanguage = cmbLanguage.SelectedIndex == 0 ? "ar" : "fr";
        LanguageManager.Instance.CurrentLanguage = selectedLanguage;
        UpdateLanguage();
    }

    private void UpdateLanguage()
    {
        var lang = LanguageManager.Instance;

        // تحديث النصوص في الشريط الجانبي سيتم من خلال إعادة إنشاء العناصر
        // أو يمكن إضافة دالة تحديث اللغة للشريط الجانبي لاحقاً

        lblWelcome.Text = lang.GetText("Welcome");
    }

    #endregion
}
