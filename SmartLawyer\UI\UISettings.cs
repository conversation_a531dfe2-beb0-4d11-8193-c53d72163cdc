using System;
using System.Drawing;
using System.IO;
using System.Windows.Forms;
using Newtonsoft.Json;
using SmartLawyer.Localization;

namespace SmartLawyer.UI
{
    public class UISettings
    {
        private static UISettings _instance;
        private static readonly object _lock = new object();
        private string _settingsPath = Path.Combine(AppDomain.CurrentDomain.BaseDirectory, "ui_settings.json");

        private UISettings()
        {
            LoadSettings();
        }

        public static UISettings Instance
        {
            get
            {
                if (_instance == null)
                {
                    lock (_lock)
                    {
                        if (_instance == null)
                            _instance = new UISettings();
                    }
                }
                return _instance;
            }
        }

        // إعدادات الثيم
        public ThemeType CurrentTheme { get; set; } = ThemeType.Light;
        public bool EnableAnimations { get; set; } = true;
        public int AnimationSpeed { get; set; } = 300; // milliseconds
        public bool EnableShadows { get; set; } = true;
        public int BorderRadius { get; set; } = 8;

        // إعدادات الخطوط
        public string FontFamily { get; set; } = "Segoe UI";
        public float FontSize { get; set; } = 10f;
        public bool UseBoldHeaders { get; set; } = true;

        // إعدادات اللغة والاتجاه
        public string Language { get; set; } = "ar";
        public bool AutoDetectLanguageDirection { get; set; } = true;
        public bool ShowLanguageToggle { get; set; } = true;

        // إعدادات النوافذ
        public bool RememberWindowPositions { get; set; } = true;
        public bool MaximizeOnStart { get; set; } = true;
        public bool ShowSplashScreen { get; set; } = true;

        // إعدادات الألوان المخصصة
        public Color? CustomPrimaryColor { get; set; }
        public Color? CustomSecondaryColor { get; set; }
        public Color? CustomBackgroundColor { get; set; }

        // إعدادات التفاعل
        public bool EnableHoverEffects { get; set; } = true;
        public bool EnableClickEffects { get; set; } = true;
        public bool EnableKeyboardShortcuts { get; set; } = true;
        public bool ShowTooltips { get; set; } = true;

        // إعدادات الجداول
        public bool AlternateRowColors { get; set; } = true;
        public bool ShowGridLines { get; set; } = true;
        public int RowHeight { get; set; } = 35;

        public event Action OnSettingsChanged;

        public void LoadSettings()
        {
            try
            {
                if (File.Exists(_settingsPath))
                {
                    string json = File.ReadAllText(_settingsPath);
                    var settings = JsonConvert.DeserializeObject<UISettings>(json);
                    
                    if (settings != null)
                    {
                        CopyPropertiesFrom(settings);
                    }
                }
            }
            catch (Exception ex)
            {
                // في حالة فشل التحميل، استخدم الإعدادات الافتراضية
                System.Diagnostics.Debug.WriteLine($"Failed to load UI settings: {ex.Message}");
            }
        }

        public void SaveSettings()
        {
            try
            {
                string json = JsonConvert.SerializeObject(this, Formatting.Indented);
                File.WriteAllText(_settingsPath, json);
                OnSettingsChanged?.Invoke();
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"Failed to save UI settings: {ex.Message}");
            }
        }

        private void CopyPropertiesFrom(UISettings source)
        {
            CurrentTheme = source.CurrentTheme;
            EnableAnimations = source.EnableAnimations;
            AnimationSpeed = source.AnimationSpeed;
            EnableShadows = source.EnableShadows;
            BorderRadius = source.BorderRadius;
            FontFamily = source.FontFamily;
            FontSize = source.FontSize;
            UseBoldHeaders = source.UseBoldHeaders;
            Language = source.Language;
            AutoDetectLanguageDirection = source.AutoDetectLanguageDirection;
            ShowLanguageToggle = source.ShowLanguageToggle;
            RememberWindowPositions = source.RememberWindowPositions;
            MaximizeOnStart = source.MaximizeOnStart;
            ShowSplashScreen = source.ShowSplashScreen;
            CustomPrimaryColor = source.CustomPrimaryColor;
            CustomSecondaryColor = source.CustomSecondaryColor;
            CustomBackgroundColor = source.CustomBackgroundColor;
            EnableHoverEffects = source.EnableHoverEffects;
            EnableClickEffects = source.EnableClickEffects;
            EnableKeyboardShortcuts = source.EnableKeyboardShortcuts;
            ShowTooltips = source.ShowTooltips;
            AlternateRowColors = source.AlternateRowColors;
            ShowGridLines = source.ShowGridLines;
            RowHeight = source.RowHeight;
        }

        public void ApplyToForm(Form form)
        {
            if (form == null) return;

            // تطبيق الثيم
            ThemeManager.Instance.CurrentTheme = CurrentTheme;

            // تطبيق اللغة
            if (!string.IsNullOrEmpty(Language))
            {
                LanguageManager.Instance.CurrentLanguage = Language;
            }

            // تطبيق الخطوط
            ApplyFontsToControl(form);

            // تطبيق الألوان المخصصة
            ApplyCustomColors(form);

            // تطبيق إعدادات النافذة
            if (MaximizeOnStart && form.WindowState != FormWindowState.Maximized)
            {
                form.WindowState = FormWindowState.Maximized;
            }

            // تطبيق إعدادات اللغة
            LanguageManager.Instance.ApplyLanguageToForm(form);
        }

        private void ApplyFontsToControl(Control control)
        {
            if (control == null) return;

            try
            {
                Font newFont = new Font(FontFamily, FontSize);
                Font boldFont = new Font(FontFamily, FontSize, FontStyle.Bold);

                if (control is Label label && UseBoldHeaders)
                {
                    control.Font = boldFont;
                }
                else
                {
                    control.Font = newFont;
                }

                // تطبيق على العناصر الفرعية
                foreach (Control child in control.Controls)
                {
                    ApplyFontsToControl(child);
                }
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"Failed to apply fonts: {ex.Message}");
            }
        }

        private void ApplyCustomColors(Control control)
        {
            if (control == null) return;

            var theme = ThemeManager.Instance;

            // تطبيق الألوان المخصصة إذا كانت محددة
            if (CustomPrimaryColor.HasValue)
            {
                if (control is ModernButton button)
                {
                    button.BackColor = CustomPrimaryColor.Value;
                }
            }

            if (CustomBackgroundColor.HasValue)
            {
                if (control is Form || control is Panel)
                {
                    control.BackColor = CustomBackgroundColor.Value;
                }
            }

            // تطبيق على العناصر الفرعية
            foreach (Control child in control.Controls)
            {
                ApplyCustomColors(child);
            }
        }

        public void ResetToDefaults()
        {
            CurrentTheme = ThemeType.Light;
            EnableAnimations = true;
            AnimationSpeed = 300;
            EnableShadows = true;
            BorderRadius = 8;
            FontFamily = "Segoe UI";
            FontSize = 10f;
            UseBoldHeaders = true;
            Language = "ar";
            AutoDetectLanguageDirection = true;
            ShowLanguageToggle = true;
            RememberWindowPositions = true;
            MaximizeOnStart = true;
            ShowSplashScreen = true;
            CustomPrimaryColor = null;
            CustomSecondaryColor = null;
            CustomBackgroundColor = null;
            EnableHoverEffects = true;
            EnableClickEffects = true;
            EnableKeyboardShortcuts = true;
            ShowTooltips = true;
            AlternateRowColors = true;
            ShowGridLines = true;
            RowHeight = 35;

            SaveSettings();
        }

        public void SetTheme(ThemeType theme)
        {
            CurrentTheme = theme;
            SaveSettings();
        }

        public void SetLanguage(string language)
        {
            Language = language;
            LanguageManager.Instance.CurrentLanguage = language;
            SaveSettings();
        }

        public void SetFont(string fontFamily, float fontSize)
        {
            FontFamily = fontFamily;
            FontSize = fontSize;
            SaveSettings();
        }

        public void SetCustomColors(Color? primary = null, Color? secondary = null, Color? background = null)
        {
            if (primary.HasValue) CustomPrimaryColor = primary;
            if (secondary.HasValue) CustomSecondaryColor = secondary;
            if (background.HasValue) CustomBackgroundColor = background;
            SaveSettings();
        }

        public void ToggleAnimations()
        {
            EnableAnimations = !EnableAnimations;
            SaveSettings();
        }

        public void SetAnimationSpeed(int speed)
        {
            AnimationSpeed = Math.Max(100, Math.Min(1000, speed)); // بين 100ms و 1000ms
            SaveSettings();
        }
    }
}
